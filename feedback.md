# Feedback

## Feedback Overview


| Sheets  | ex1 | ex2 | ex3 | ex4 | ex5 | Short Comments |
|---------|-----|-----|-----|-----|-----|----------------|
| Sheet01 |  8  |  5 |  4,5  |   |     |  Good   |
| Sheet02 |  9  |  5 |  0 |   |  3,5   | Very Good!   |
| Sheet03 |  5  |  5 |  4 |   0|    5 | Almost perfect!   |
| Sheet04 | 12  | 9  |10   |   |     |Almost perfect!    |
## Additional Comments
### Sheet04
### ex1
Nice benchmarking 2 extra points for this!

#### ex2 a

A little too brief.

Euler method not mentioned (–0.5)

No connection to mathematical model ODE (–0.5)

#### ex3
```bash
before_script:
  - git submodule update --init --recursive
```
is fine too!
But normally, you would want to set a gitlab-ci variable for GTest as said in the hint.

```bash
build - all - exercises :
stage : build
variables :
GIT_SUBMODULE_STRATEGY : recursive

```
### Sheet03
#### ex3c
Forgot to mention the control block.

#### ex3d
Your solution is very good. However, please remember to specify the type of the arguments in the function signature of lambda expressions. For example, in:
```c++
auto square = []( auto x )
```
The parameter **x** is generic, making this a generic lambda function. We are strict about explicitly naming types(e.g. template parameters types.)

### Sheet02
#### ex1
Explain the shallow and deep copy more in detail. 
##### ex4 a
**A namespace encapsulates identifiers (variables, functions, . . . ) in a new scope.**
A namespace is used to organize and group code to prevent name collisions for example with
external libraries.

##### ex4 b
No clear distinction between the rule of 5 and the rule of zero. The rule of 5 states that if you define one of the special functions (copy constructor, move constructor, copy assignment operator, move assignment operator, destructor) you should define all of them. 
The rule of zero sates that if a class does not manage memory directly, it should avoid to
explicitly define one of the mentioned member functions.


##### ex4 e
#### ex4e
Missing explanation of struct: The Vector3D struct stores x, y, z values and provides a default constructor, an implicit single argument constructor and a custom constructor to set all values.
### Sheet01

#### ex1

The private specifier is not necessary in this case, as the default access specifier for class members is private. So you can write the private members above the public members. This is a matter of style but just FYI.

Next:

They should be free-functions or friends. Not part of the class...:
```c++
Rational operator +( const Rational & r1 , const Rational & r2 ) ;
Rational operator *( const Rational & r1 , const Rational & r2 ) ;
Rational operator -( const Rational & r1 , const Rational & r2 ) ;
Rational operator /( const Rational & r1 , const Rational & r2 )
```

#### ex3 e

Differentiate between the template types:
```txt
The code snippet illustrates a template function with a type and a non-type template parameter with default values
```
