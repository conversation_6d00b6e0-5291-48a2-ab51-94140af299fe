## 1
Namespace is a container, it creates a scope where variable and function names are grouped together. To access these identifiers from a namespace from outside of this namespace, one needs to prefix the identifier with the namespace name.
Might be useful in big projects where the same identifier can be accidentally defined in different, unrelated ways. Namespace can prevent identifier collisions.

## 2
Rule of 5 and the rule of zero
In c++ there are 5 types of special functions (move, copy constructor, destructor, copy and move assigment operator). This rule states that most often the developer either is fine with compiler-generated default methods (and thus has to explicitly define 0 of them), or has to explicitly write all 5 of them.

## 3
```c++
bool < short < float < long == double
```

## 4
```c++
struct VectorWrapper {
    VectorWrapper() : comp{0, 0, 0} {};
    VectorWrapper(const VectorWrapper &v) : comp{v.comp} {}
    VectorWrapper(VectorWrapper &&v) : comp{std::move(v.comp)} {};
    VectorWrapper(double a, double b, double c) : comp{a, b, c} {};
    VectorWrapper &operator=(const VectorWrapper &v) {
        comp = v.comp;
        return *this;
    }
    VectorWrapper &operator=(VectorWrapper &&v) noexcept {
        comp = std::move(v.comp);
        return *this;
    }
    std::vector<double> comp;
 }

```
Given are (in this order):
- the default constructor
- the copy constructor
- the move constructor

- constructor that takes 3 doubles
- copy assigment operator
- move assigment operator

```c++
 VectorWrapper u;
 VectorWrapper v(1, 2, 3);
 VectorWrapper w = v;
 w = std::move(u);
 ```
 First, the default constructor is called. Then, the explicit constructor is called and int arguments are promoted to doubles, I think.

 Third line: copy constructor is called that initializes w.

 Finally, move assigment method is called.

 ## 5
 ```c++
struct Vector3D {
    double x, y, z;
    Vector3D() : x(0.0), y(0.0), z(0.0){};
    Vector3D(double value) : x(value), y(value), z(value){};
    Vector3D(double x, double y, double z) : x(x), y(y), z(z) {}
 };
 double operator*(const Vector3D &a, const Vector3D &b) {
    return a.x * b.x + a.y * b.y + a.z * b.z;
 }
 Vector3D operator*(double scalar, const Vector3D &b) {
    return {scalar * b.x, scalar * b.y, scalar * b.z};
 }
 ```
Functionality: constructors for Vector3D are defined and * operator is overloaded that allows to multiply 2 vectors or a double with a vector. 
Note that there is no exmplicit keyword in the second constructor, that can give some hazards.
 ```c++
 Vector3D a(1, 2, 3);
 Vector3D b(0,-1, 2);
 auto result1 = a * b;
 auto result2 = a * 2.0;
 auto result3 = 2.0 * a;
 ```
`result1` is multiplying 2 `vector3D` => `double`, value=`4`



`result2` multiples vector by a `double`, for this order of variables, the `*` operator is not defined... However, `2.0` can be converted to `Vector3D` using the second constructor, so, the result will be vector*vector, i.e. a `double`. value=`12`

`result3` uses the last overloaded `*` operator and the result is `Vector3D`. value=`Vector3D(2,4,6)`