#include "LinkedListConst.hpp"
#include <iostream>
#include <stdexcept>

Node* LinkedListConst::first() const {
  return firstNode;
}

Node* LinkedListConst::next(const Node* n) const {
  return n->next_;
}

void LinkedListConst::append(int i) {
  if (firstNode == nullptr) {
    Node* newNode = new Node(i);
    firstNode = newNode;
  } else {
    Node* currentNode = firstNode;
    while (currentNode->next_ != nullptr) {
      currentNode = currentNode->next_;
    }
    Node* newNode = new Node(i);
    currentNode->next_ = newNode;
  }
  maxValid_ = false;
}

void LinkedListConst::insert(Node* n, int i) {
  Node* newNode = new Node(i);

  if (firstNode == nullptr) {
    firstNode = newNode;
    maxValid_ = false;
    return;
  }

  Node* currentNode = firstNode;
  Node* prevNode = nullptr;
  while (currentNode != nullptr && currentNode != n) {
    prevNode = currentNode;
    currentNode = currentNode->next_;
  }

  if (currentNode == n) {
    std::cout << "Node n found, inserting" << std::endl;
    if (prevNode == nullptr) {
      // inserting before the first node
      newNode->next_ = firstNode;
      firstNode = newNode;
    } else {
      prevNode->next_ = newNode;
      newNode->next_ = currentNode;
    }
  } else {
    std::cout << "Node n not found, inserting at the end" << std::endl;
    prevNode->next_ = newNode;
  }
  maxValid_ = false;
}

void LinkedListConst::erase(Node* n) {
  Node* currentNode = firstNode;
  Node* prevNode = nullptr;
  if (firstNode == n) {
    firstNode = firstNode->next_;
    delete n;
    maxValid_ = false;
    return;
  }
  while (currentNode != nullptr && currentNode != n) {
    prevNode = currentNode;
    currentNode = currentNode->next_;
  }
  if (currentNode == n) {
    std::cout << "Node n found, erasing" << std::endl;
    prevNode->next_ = currentNode->next_;
    delete currentNode;
    maxValid_ = false;
  } else {
    std::cout << "Node n not found, not erasing anything" << std::endl;
  }
}

LinkedListConst::~LinkedListConst() {
  Node* currentNode = firstNode;
  while (currentNode != nullptr) {
    Node* next = currentNode->next_;
    delete currentNode;
    currentNode = next;
  }
}

bool LinkedListConst::operator==(const LinkedListConst& other) const {
  Node* iter = first();
  Node* iterOther = other.first();
  while (iter != nullptr && iterOther != nullptr &&
         iter->value_ == iterOther->value_) {
    iter = iter->next_;
    iterOther = iterOther->next_;
  }
  return iter == nullptr && iterOther == nullptr;
}

int LinkedListConst::max() const {
  if (firstNode == nullptr) {
    throw std::runtime_error("Linked list is empty");
  }
  if (!maxValid_) {
    int m = firstNode->value_;
    for (Node* ptr = firstNode->next_; ptr; ptr = ptr->next_) {
      m = std::max(m, ptr->value_);
    }
    maxCache_ = m;
    maxValid_ = true;
  }
  return maxCache_;
}

bool LinkedListConst::isValid() const {
  return maxValid_;
}

void printList(const LinkedListConst& list) {
  for (const Node* n = list.first(); n != 0; n = list.next(n))
    std::cout << n->value_ << std::endl;
  std::cout << "max: " << list.max() << std::endl;
}