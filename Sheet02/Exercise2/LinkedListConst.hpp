#pragma once
#include <iostream>
#include "nodeConst.hpp"

class LinkedListConst {
 public:
  LinkedListConst() = default;      // Create an empty list
  ~LinkedListConst();               // Clean up list and all nodes
  Node* first() const;              // Return pointer to first entry
  Node* next(const Node* n) const;  // Return pointer to node after n
  void append(int i);               // Append a value to the end
  void insert(Node* n, int i);      // Insert a value before n
  void erase(Node* n);              // Remove n from the list
  bool operator==(const LinkedListConst& other) const;
  int max() const;
  bool isValid() const;

 private:
  Node* firstNode = nullptr;
  mutable bool maxValid_ = false;
  mutable int maxCache_;
};

void printList(const LinkedListConst& list);