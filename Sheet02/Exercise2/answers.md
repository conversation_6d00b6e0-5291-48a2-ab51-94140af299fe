### Ex2 (a)

## Which and why some methods and parameters should be const in this example?

Any member function that should only have read access should carry the `const` qualifier. This ensures that the function does not modify any member variables of the class.
In the `LinkedListWithRawPointers` class, only the `first`, `next` and `operator==` functions should be `const` because they should only read the data and not modify it.

## Which and why some methods and parameters shouldn’t be const in this example?

All other member functions that modify the state of the object should not be `const`. This includes `append`, `erase`, `insert` and the destructor. These functions need to modify the member
variables of the class, so they should not be `const`.

## What about data members of LinkedListWithRawPointers, and what about the Node class?

The data members of `LinkedListWithRawPointers` should be `const` if they are not meant to be modified after initialization. But both for the `LinkedListWithRawPointers` and `Node` classes,
the data members need to be mutable to allow for modifying values aswell as the next pointer.

### Ex2 (b)

## What effect does the keyword mutable have and why is it needed here?

The `mutable` keyword allows a member variable to be modified even from inside a `const` member function. Without `mutable`, it would not be allowed to do so. This allows to be callable on
a `const` object and still be able to update the `maxValid_` and `maxCache_` member variables. Like this, we can have a `const` object but can still keep track of the maximum valid and cached values.

## What would be inefficient about updating the stored value every time the list is modified?

If there are many stores and delets, this also implies many updates to the `maxValid_` and `maxCache_` member variables. For inserting/appending it is just a constant check but for erasing the
whole list has to be scanned to find the new maximum. This would be inefficient and slow down the performance of the list. A lazy update is more efficient because it only updates the values when they are needed.

### Ex2 (c)

## What exactly do the two instances of const refer to, especially the second one?

The first `const` applies to the list itself, meaning that the `printList` function may not modify the list, which is passed by reference. The second `const` says that the pointer `n`
points to a `const Node`, meaning that the pointer can be changed to point to another node, but the node itself cannot be modified (read only). 