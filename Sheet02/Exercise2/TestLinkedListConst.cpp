#include "LinkedListConst.hpp"
#include "gtest/gtest.h"

TEST(TestLinkedListConst, TestEqualOperator) {
  LinkedListConst list1;
  list1.append(2);
  list1.append(3);

  LinkedListConst list2;
  list2.append(2);
  list2.append(3);

  EXPECT_TRUE(list1 == list2);
}

TEST(TestLinkedListConst, TestInsertStart) {
  LinkedListConst list;
  list.append(2);
  list.append(3);

  list.insert(list.first(), 10);

  LinkedListConst expectedList;
  expectedList.append(10);
  expectedList.append(2);
  expectedList.append(3);

  EXPECT_TRUE(list == expectedList);
}

TEST(TestLinkedListConst, TestInsertMiddel) {
  LinkedListConst list;
  list.append(2);
  list.append(3);

  list.insert(list.next(list.first()), 1);

  LinkedListConst expectedList;
  expectedList.append(2);
  expectedList.append(1);
  expectedList.append(3);

  EXPECT_TRUE(list == expectedList);
}

TEST(TestLinkedListConst, TestEraseFront) {
  LinkedListConst list;
  list.append(2);
  list.append(3);

  list.erase(list.first());

  LinkedListConst expectedList;
  expectedList.append(3);

  EXPECT_TRUE(list == expectedList);
}

TEST(TestLinkedListConst, TestMax) {
  LinkedListConst list;
  list.append(3);
  list.append(5);
  list.append(2);
  list.insert(list.next(list.first()), 1);

  int maxValue = list.max();
  bool isMaxValid = list.isValid();

  EXPECT_EQ(maxValue, 5);
  EXPECT_EQ(isMaxValid, true);
}

TEST(LinkedListConstTest, PrintListOutputsCorrectly) {
  LinkedListConst list;
  list.append(7);
  list.append(3);
  list.append(9);

  testing::internal::CaptureStdout();
  printList(list);
  std::string output = testing::internal::GetCapturedStdout();

  std::ostringstream expected;
  expected << "7\n";
  expected << "3\n";
  expected << "9\n";
  expected << "max: 9\n";

  EXPECT_EQ(output, expected.str());
}

int main() {
  ::testing::InitGoogleTest();
  return RUN_ALL_TESTS();
}