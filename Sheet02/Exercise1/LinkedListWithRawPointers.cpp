#include "LinkedListWithRawPointers.hpp"

LinkedListWithRawPointers::~LinkedListWithRawPointers() {
    Node *iter = first();
    while (iter != nullptr) {
        Node *toDelete = iter;
        iter = next(iter);
        delete toDelete;
    }
}

Node *LinkedListWithRawPointers::first() const {
    return firstNode;
}

Node *LinkedListWithRawPointers::next(const Node *n) const {
    return n->next;
}

void LinkedListWithRawPointers::append(int i) {
    insert(nullptr, i);
}

void LinkedListWithRawPointers::insert(Node *n, int i) {
    if (n == firstNode) {
        firstNode = new Node(i, n);
        return;
    }

    Node *iter = first();
    while (next(iter) != n)
        iter = next(iter);

    iter->next = new Node(i, n);
}

void LinkedListWithRawPointers::erase(Node *n) {
    if (n == firstNode) {
        firstNode = next(n);
        delete n;
        return;
    }

    Node *iter = first();
    while (next(iter) != n)
        iter = next(iter);

    iter->next = next(n);
    delete n;
}

bool LinkedListWithRawPointers::operator==(const LinkedListWithRawPointers &other) const {
    Node *iter = first();
    Node *iterOther = other.first();
    while (iter != nullptr && iterOther != nullptr && iter->value == iterOther->value) {
        iter = next(iter);
        iterOther = next(iterOther);
    }

    return iter == nullptr && iterOther == nullptr;
}

