## What is special about a pointer having the value `nullptr`?

It does not point to a valid memory location.

## What happens if one copies the list?

Because the default copy constructor is used, only a shallow copy is made. As a result, the `firstNode` pointer in both
the original and the copy points to the same node. All modifications to nodes after the original `firstNode` are
therefore shared between both lists.

## What happens if both lists are deleted?

The same nodes will be deleted twice, which leads to an error.

## How can this problem be avoided?

A copy constructor that recreates the list with new and independent nodes could be used.