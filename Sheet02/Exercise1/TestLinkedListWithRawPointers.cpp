#include "LinkedListWithRawPointers.hpp"
#include "gtest/gtest.h"

TEST(TestLinkedListWithRawPointers, TestEqualOperator) {
  LinkedListWithRawPointers list1;
  list1.append(2);
  list1.append(3);

  LinkedListWithRawPointers list2;
  list2.append(2);
  list2.append(3);

  EXPECT_TRUE(list1 == list2);
}

TEST(TestLinkedListWithRawPointers, TestInsertStart) {
  LinkedListWithRawPointers list;
  list.append(2);
  list.append(3);

  list.insert(list.first(), 10);

  LinkedListWithRawPointers expectedList;
  expectedList.append(10);
  expectedList.append(2);
  expectedList.append(3);

  EXPECT_TRUE(list == expectedList);
}

TEST(TestLinkedListWithRawPointers, TestInsertMiddel) {
  LinkedListWithRawPointers list;
  list.append(2);
  list.append(3);

  list.insert(list.next(list.first()), 1);

  LinkedListWithRawPointers expectedList;
  expectedList.append(2);
  expectedList.append(1);
  expectedList.append(3);

  EXPECT_TRUE(list == expectedList);
}

TEST(TestLinkedListWithRawPointers, TestEraseFront) {
  LinkedListWithRawPointers list;
  list.append(2);
  list.append(3);

  list.erase(list.first());

  LinkedListWithRawPointers expectedList;
  expectedList.append(3);

  EXPECT_TRUE(list == expectedList);
}

int main() {
  ::testing::InitGoogleTest();
  return RUN_ALL_TESTS();
}
