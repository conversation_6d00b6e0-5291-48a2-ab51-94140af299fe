#pragma once
#include "Node.hpp"

class LinkedListWithRawPointers {
public:
    LinkedListWithRawPointers() = default; // Create an empty list
    ~LinkedListWithRawPointers(); // Clean up list and all nodes
    Node *first() const; // Return pointer to first entry
    Node *next(const Node *n) const; // Return pointer to node after n
    void append(int i); // Append a value to the end
    void insert(Node *n, int i); // Insert a value before n
    void erase(Node *n); // Remove n from the list
    bool operator==(const LinkedListWithRawPointers &other) const;

private:
    Node *firstNode = nullptr;
};
