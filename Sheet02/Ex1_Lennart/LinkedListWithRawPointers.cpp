#include "LinkedListWithRawPointers.hpp"
#include <iostream>

Node* LinkedListWithRawPointers::first() const {
  return firstNode;
}

Node* LinkedListWithRawPointers::next(const Node* n) const {
  return n->next_;
}

void LinkedListWithRawPointers::append(int i) {
  if (firstNode == nullptr) {
    Node* newNode = new Node(i);
    firstNode = newNode;
  } else {
    Node* currentNode = firstNode;
    while (currentNode->next_ != nullptr) {
      currentNode = currentNode->next_;
    }
    Node* newNode = new Node(i);
    currentNode->next_ = newNode;
  }
}

void LinkedListWithRawPointers::insert(Node* n, int i) {
  Node* newNode = new Node(i);

  if (firstNode == nullptr) {
    firstNode = newNode;
    return;
  }

  Node* currentNode = firstNode;
  Node* prevNode = nullptr;
  while (currentNode != nullptr && currentNode != n) {
    prevNode = currentNode;
    currentNode = currentNode->next_;
  }

  if (currentNode == n) {
    std::cout << "Node n found, inserting" << std::endl;
    if (prevNode == nullptr) {
      // inserting before the first node
      newNode->next_ = firstNode;
      firstNode = newNode;
    } else {
      prevNode->next_ = newNode;
      newNode->next_ = currentNode;
    }
  } else {
    std::cout << "Node n not found, inserting at the end" << std::endl;
    prevNode->next_ = newNode;
  }
}

void LinkedListWithRawPointers::erase(Node* n) {
  Node* currentNode = firstNode;
  Node* prevNode = nullptr;
  if (firstNode == n) {
    firstNode = firstNode->next_;
    delete n;
    return;
  }
  while (currentNode != nullptr && currentNode != n) {
    prevNode = currentNode;
    currentNode = currentNode->next_;
  }
  if (currentNode == n) {
    std::cout << "Node n found, erasing" << std::endl;
    prevNode->next_ = currentNode->next_;
    delete currentNode;
  } else {
    std::cout << "Node n not found, not erasing anything" << std::endl;
  }
}

LinkedListWithRawPointers::~LinkedListWithRawPointers() {
  Node* currentNode = firstNode;
  while (currentNode != nullptr) {
    Node* next = currentNode->next_;
    delete currentNode;
    currentNode = next;
  }
}

bool LinkedListWithRawPointers::operator==(
    const LinkedListWithRawPointers& other) const {
  Node* iter = first();
  Node* iterOther = other.first();
  while (iter != nullptr && iterOther != nullptr &&
         iter->value_ == iterOther->value_) {
    iter = iter->next_;
    iterOther = iterOther->next_;
  }

  return iter == nullptr && iterOther == nullptr;
}