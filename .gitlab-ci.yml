image: rikorose/gcc-cmake
stages:
- build
- tests

build-all-exercises:
  stage: build
  before_script:
    - git submodule update --init --recursive || true
  script:
    - mkdir -p build
    - cmake -S . -B build -Dgtest_DIR="${CI_PROJECT_DIR}/googletest"
    - cmake --build build
  artifacts:
    paths:
      - build/

test-all-exercises:
  stage: tests
  dependencies:
    - build-all-exercises
  script:
    - cd build
    - ctest --output-on-failure