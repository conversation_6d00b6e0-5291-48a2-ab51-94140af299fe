## a)
`Name two different ways how static polymorphism can be achieved in C++.`

Using function and operator overloading (e.g. same function name, but different signatures - different argument types), or using CRTP.

## b) 
`Explain each letter of the acronym SOLID in one sentence.`

S: Single Responsibility. A class should only do one concrete thing instead of many unrelated things.

O: Open/Closed. Classes should not be tampered with, rather, they should be extended if new functionality is requested.

L: Liskov Substitution. Wherever a superclass is used, it should be possible to use any of the derived classes in its place.

I: Interface Segregation. Clients should never be forced to implement methods that they do not intend to use.

D: Dependency Inversion. Class usage should not change drastically when details of some API change, i.e. classes should depend on interfaces and not on the details of some implementation.

## c)
`Explain what an iterator object is and where it might be useful`

An iterator is an object that implements increment and dereference methods and thus allows to iterate over its elements.

It can be useful in that it allows to reuse for loops and algorithms like sorting without having to implement them from scratch.

## d)
` Replace the dynamic polymorphism with a CRTP and discuss where this might or might not be useful.`
```c++
 struct Abstract {
    virtual void implementation() = 0;
 };

 struct Foo : public Abstract {
    void implementation() override {
        std::cout << "foo" << std::endl;
    }
 };

 struct Bar : public Abstract {
    void implementation() override {
        std::cout << "bar" << std::endl;
    }
 };
```

```c++
template<class Derived>
struct Abstract 
{
    void implementation() {
        static_cast<Derived*>(this)->implementation();
    }
};

struct Foo : Abstract<Foo>
{
    void implementation() { std::cout << "foo\n"; }
};

struct Bar : Abstract<Bar>
{
    void implementation() { std::cout << "bar\n"; }
};
```
This might be useful when developer wants to ensure that derived classes implement all methods of the base class - CRTP gives compile-time error in that case.

This might be useful to improve performance speed, as it avoids cost of virtual functions. 

This might be harmful when the type of variables needs to be changed via `new` command, as it is not possible.

## e)
`Explain the details of the following code snippet and the underlying techniques:`
```c++
//1)
template <typename T> T identity(T t) { return t; }

//2)
template <> int identity(int t) { return t; }

//3)
double identity(int t) { return t; }
```
1.is a 'normal' template. 2 is an explicit specialization that defines the behaviour when the templated function gets type int. 3. is not a template at all and it overloads the identity function when argument is of int type.

```c++
 auto result1 = identity(0.0); //1) function is used, i.e. the normal template, and result is 0.0
 auto result2 = identity(0); //3) function is used, because it is the most specific one and it is not a template. Evaluates to 0.0
 auto result3 = identity<>(0); //2) function is used, as the signature explicitly calls for a templated function to be called. Evaluated to 0
```
