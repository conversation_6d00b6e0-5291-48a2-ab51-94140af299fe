`What properties of a particular implementation of a function determine whether CRTP pays off here?`

The intended usage determines it: It would not pay off if I wanted to put the objects of derived classes to some container like std::vector, because that would not be possible.

If calls happen many times, which is the case in our example, then less vtable lookups will happen and some performance benefit might be visible.




