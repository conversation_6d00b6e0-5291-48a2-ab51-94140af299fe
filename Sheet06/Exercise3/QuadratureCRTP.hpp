#pragma once

#include <cmath>
#include <stdexcept>
#include <vector>

//
// CRTP Functor interface
//
template <typename Derived>
class Functor {
public:
    double operator()(double x) const {
        return static_cast<const Derived*>(this)->operator()(x);
    }
};

//
// Cos functor
//
class Cos : public Functor<Cos> {
public:
    double operator()(double x) const {
        return std::cos(x);
    }
};

//
// Polynomial functor
//
class Polynomial : public Functor<Polynomial> {
public:
    explicit Polynomial(const std::vector<double>& coefficients)
        : coeffs_(coefficients) {}

    double operator()(double x) const {
        double result = 0.0;
        double x_power = 1.0;
        for (double coeff : coeffs_) {
            result += coeff * x_power;
            x_power *= x;
        }
        return result;
    }

private:
    std::vector<double> coeffs_;
};

//
// CRTP QuadratureRule interface
//
template <typename Derived>
class QuadratureRule {
public:
    template <typename CosOrPolynomialFunction>
    double integrate(
        const Functor<CosOrPolynomialFunction>& f,
        double a,
        double b
    ) const {
        return static_cast<const Derived*>(this)->integrate(f, a, b);
    }

    int degreeOfExactness() const {
        return static_cast<const Derived*>(this)->degreeOfExactness();
    }

    double ExpectedOrderOfConvergence() const {
        return static_cast<const Derived*>(this)->ExpectedOrderOfConvergence();
    }
};

//
// TrapezRule
//
class TrapezRule : public QuadratureRule<TrapezRule> {
public:
    template <typename CosOrPolynomialFunction>
    double integrate(
        const Functor<CosOrPolynomialFunction>& f,
        double a,
        double b
    ) const {
        return (b - a) * (f(a) + f(b)) / 2.0;
    }

    int degreeOfExactness() const { return 1; }
    double ExpectedOrderOfConvergence() const { return 2.0; }
};

//
// SimpsonRule
//
class SimpsonRule : public QuadratureRule<SimpsonRule> {
public:
    template <typename CosOrPolynomialFunction>
    double integrate(
        const Functor<CosOrPolynomialFunction>& f,
        double a,
        double b
    ) const {
        double m = (a + b) / 2.0;
        return (b - a) / 6.0 * (f(a) + 4.0 * f(m) + f(b));
    }

    int degreeOfExactness() const { return 3; }
    double ExpectedOrderOfConvergence() const { return 4.0; }
};

//
// CompositeRule
//
template <typename Rule>
class CompositeRule : public QuadratureRule<CompositeRule<Rule>> {
public:
    explicit CompositeRule(const Rule& baseRule, int numSubIntervals)
        : baseRule_(baseRule), N_(numSubIntervals) {
        if (N_ <= 0) {
            throw std::invalid_argument(
                "Number of subintervals must be positive"
            );
        }
    }

    template <typename CosOrPolynomialFunction>
    double integrate(
        const Functor<CosOrPolynomialFunction>& f,
        double a,
        double b
    ) const {
        double result = 0.0;
        double h = (b - a) / static_cast<double>(N_);
        for (int i = 0; i < N_; ++i) {
            double left = a + i * h;
            double right = left + h;
            result += baseRule_.integrate(f, left, right);
        }
        return result;
    }

    int degreeOfExactness() const { return baseRule_.degreeOfExactness(); }
    double ExpectedOrderOfConvergence() const {
        return baseRule_.ExpectedOrderOfConvergence();
    }

private:
    Rule baseRule_;
    int N_;
};

//
// Error Order of Convergence helper
//
inline double EOC(double factor, double EN, double E2N) {
    return std::log(EN / E2N) / std::log(factor);
}
