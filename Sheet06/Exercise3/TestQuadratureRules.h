#pragma once

#include "QuadratureCRTP.hpp"
#include "gtest/gtest.h"

class TestQuadratureRules : public ::testing::Test {
protected:
  TrapezRule trapezRule;
  SimpsonRule simpsonRule;

  Polynomial linear{ {1, -1} };
  Polynomial quadratic{ {1, 0, -1} };
  Polynomial cubic{ {0, 0, 0, 1} };
  Polynomial quartic{ {0, 0, 0, 0, 1} };

  Cos cosFunc;

  double a_poly = 0.0;
  double b_poly = 1.0;

  double a_cos = -M_PI / 2.0;
  double b_cos =  M_PI / 2.0;
};
