#include "TestQuadratureRules.h"
#include "QuadratureCRTP.hpp"
#include "gtest/gtest.h"


TEST_F(TestQuadratureRules, TestTrapezRule) {
  double value = trapezRule.integrate(linear, a_poly, b_poly);
  EXPECT_DOUBLE_EQ(value, 0.5);
}

TEST_F(TestQuadratureRules, TestSimpsonRule) {
  double value = simpsonRule.integrate(quadratic, a_poly, b_poly);
  EXPECT_DOUBLE_EQ(value, 2.0/3.0);
}

TEST_F(TestQuadratureRules, TestCompositeTrapezEOC) {
  const int N = 100;

  CompositeRule ruleN(trapezRule, N);
  CompositeRule rule2N(trapezRule, 2 * N);

  double resN = ruleN.integrate(cosFunc, a_cos, b_cos);
  double res2N = rule2N.integrate(cosFunc, a_cos, b_cos);

  double exact = 2.0;

  double errN = std::abs(resN - exact);
  double err2N = std::abs(res2N - exact);

  EXPECT_NEAR(EOC(2.0, errN, err2N), ruleN.ExpectedOrderOfConvergence(), 1e-3);
}

TEST_F(TestQuadratureRules, TestSimpsonRuleOnCubic) {
  double value = simpsonRule.integrate(cubic, a_poly, b_poly);
  EXPECT_DOUBLE_EQ(value, 0.25);
}

TEST_F(TestQuadratureRules, TestTrapezRuleQuadraticNotExact) {
  double value = trapezRule.integrate(quadratic, a_poly, b_poly);
  EXPECT_FALSE(std::abs(value - 2.0/3.0) < 1e-12);

}

TEST_F(TestQuadratureRules, ConvergenceTestTrapezAndSimpson) {
  std::vector<int> N_values{2, 4, 8, 16, 32, 64, 128};
  double exact = 2.0;

  std::vector<double> errorsTrapez;
  std::vector<double> errorsSimpson;

  for (int N : N_values) {
    CompositeRule trapezComposite(trapezRule, N);
    CompositeRule simpsonComposite(simpsonRule, N);

    double resT = trapezComposite.integrate(cosFunc, a_cos, b_cos);
    double resS = simpsonComposite.integrate(cosFunc, a_cos, b_cos);

    errorsTrapez.push_back(std::abs(resT - exact));
    errorsSimpson.push_back(std::abs(resS - exact));
  }

  std::cout << "N\tError(Trapez)\tError(Simpson)\n";
  for (size_t i = 0; i < N_values.size(); ++i) {
    std::cout << N_values[i] << "\t" << errorsTrapez[i] << "\t" << errorsSimpson[i] << "\n";
  }
}


int main() {
  testing::InitGoogleTest();
  return RUN_ALL_TESTS();
}
