## 1)
```c++
#include <iostream>

void f()
{
    std::cout << "1";
}

template<typename T>
struct B
{
    void f()
    {
        std::cout << "2";
    }
};

template<typename T>
struct D : B<T>
{
    void g()
    {
        f();
    }
};

int main()
{
    D<int> d;
    d.g();
}

```

`1` is printed. The function `f` lookup is explained in cppquiz.org. In my own words: the `f` function of the base class is not visible by the compiler during the lookup, because the class is dependent of D via template. So the function `f` from the global scope gets executed.

The lookup procedure makes sense, but before reading the explanation my guess was that `2` would be printed.

## 2)
```c++
#include <iostream>

struct X {
  virtual void f() const { std::cout << "X"; }
};

struct Y : public X {
  void f() const { std::cout << "Y"; }
};

void print(const X &x) { x.f(); }

int main() {
  X arr[1];
  Y y1;
  arr[0] = y1;
  print(y1);
  print(arr[0]);
}
```

`YX` is printed. `arr` is an array of objects of type `X`, so, when referencing `arr[0]`, it behaves as an instance of class `X`. And `y1` is of type `Y`, so `Y` is printed.

This executes as I expected, because `arr` is an array of `X`, so I would expect to see behaviour of `X` when I access elements of `arr`.

## 3)
```c++
#include <iostream>

struct A {
  A() { foo(); }
  virtual ~A() { foo(); }
  virtual void foo() { std::cout << "1"; }
  void bar() { foo(); }
};

struct B : public A {
  virtual void foo() { std::cout << "2"; }
};

int main() {
  B b;
  b.bar();
}
```

`121` is printed. It is because during constructor execution the base class is constructed, and only then the inherited class constucts. During the destruction - first the derived class destructor is called, then (after the derived class is 'destroyed') the base class's constructor gets executed. So, `foo()` in condtructor and destructor are methods of the base class `A`. 
`b.bar()` in `main` print `2`, because `bar` method is overriden.

It was as I expected, because I already learned the order of constructor and destructor execution. It makes sense to first destroy the derived functionality before destroying the base functionality. Similarily for construction - we need to first construct the base class before adding extra functionality via inheritance.

## 4)

```c++
#include <iostream>

struct Base {
    virtual int f() = 0;
};

int Base::f() { return 1; }

struct Derived : Base {
    int f() override;
};

int Derived::f() { return 2; }

int main() {
    Derived object;
    std::cout << object.f();
    std::cout << ((Base&)object).f();
}
```
`22` is printed. `f` is a virtual function that must be explicitly implemented in all derived classes.  `int Derived::f() { return 2; }` does it. So, `object.f()` prints `2`. So does the `std::cout << ((Base&)object).f();`, because `f()` is a virtual function, it is implemented in derived classes, and thus prints `2`.

Upon reading the explanation it made sense to me: the virtual methods should indeed be implemented somewhere in the derived classes, and during the lookup that's where the compiler should be looking for the implementation of a virtual function - in (the most 'deeply') derived classes.

## 5)
```c++
#include <iostream>

class A {
public:
    A() { std::cout << "A"; }
    A(const A &) { std::cout << "a"; }
};

class B : public virtual A {
public:
    B() { std::cout << "B"; }
    B(const B &) { std::cout << "b"; }
};

class C : public virtual A {
public:
    C() { std::cout << "C"; }
    C(const C &) { std::cout << "c"; }
};

class D : B, C {
public:
    D() { std::cout << "D"; }
    D(const D &) { std::cout << "d"; }
};

int main() {
    D d1;
    D d2(d1);
}
```

` ABCDABCd` is printed. First, `ABCD` is printed by the constructors. The virtual base class `A` is constructed once, then B, C and finally D.

It gets unintuitive during the copy constructor execution. As it turns out, if a user-defined copy constructor is provided, then it is user's responsibility to also call copy constructors of the base classes. Hence `ABC` is printed before `d`. Upon reading the provided explanation makes sense - if user defines a copy constructor, they should have a good reason for it and thus c++ standart gives them the responsibility to call the copy constructors of base classes if needed. 