#include "Rational.hpp"
#include <iostream>
#include <stdexcept>

Rational::Rational() : m_num(1), m_denom(1) {}

Rational::Rational(int num, int denom) : m_num(num), m_denom(denom) {
  if (m_denom == 0) {
    throw std::invalid_argument("Division by zero is not defined");
  }
  normalize();
}

Rational::Rational(int whole_int) : m_num(whole_int), m_denom(1) {}

int Rational::numerator() const {
  return m_num;
}

int Rational::denominator() const {
  return m_denom;
}

void Rational::normalize() {
  if (m_denom == 0)
    throw std::invalid_argument{"Division by zero is not defined"};

  if (m_denom < 0) {
    m_num = -m_num;
    m_denom = -m_denom;
  }
  int greatest_common_div = gcd(std::abs(m_num), std::abs(m_denom));
  m_denom /= greatest_common_div;
  m_num /= greatest_common_div;
}

Rational& Rational::operator*=(Rational const& other) {
  m_num *= other.numerator();
  m_denom *= other.denominator();
  normalize();
  return *this;
}

Rational& Rational::operator/=(Rational const& other) {
  if (other.numerator() == 0) {
    throw std::invalid_argument("Division by zero is not defined");
  }
  m_num *= other.denominator();
  m_denom *= other.numerator();
  normalize();
  return *this;
}

Rational& Rational::operator+=(Rational const& other) {
  int num = other.denominator() * m_num;
  int other_num = other.numerator() * m_denom;
  m_denom *= other.denominator();
  m_num = num + other_num;
  normalize();
  return *this;
}

Rational& Rational::operator-=(Rational const& other) {
  int num = other.denominator() * m_num;
  int other_num = other.numerator() * m_denom;
  m_denom *= other.denominator();
  m_num = num - other_num;
  normalize();
  return *this;
}

Rational Rational::operator*(Rational const& other) {
  Rational temp = *this;
  temp *= other;
  return temp;
}

Rational Rational::operator/(Rational const& other) {
  Rational temp = *this;
  temp /= other;
  return temp;
}

Rational Rational::operator+(Rational const& other) {
  Rational temp = *this;
  temp += other;
  return temp;
}

Rational Rational::operator-(Rational const& other) {
  Rational temp = *this;
  temp -= other;
  return temp;
}

bool Rational::operator==(Rational const& other) const {
  return m_denom == other.denominator() && m_num == other.numerator();
}

int gcd(int a, int b) {
  if (a < b) {
    int tmp = a;
    a = b;
    b = tmp;
  }
  if (b == 0) {
    return a;
  }
  return gcd(b, a % b);
}

std::ostream& operator<<(std::ostream& str, const Rational& r) {
  str << r.numerator() << "/" << r.denominator();
  return str;
}