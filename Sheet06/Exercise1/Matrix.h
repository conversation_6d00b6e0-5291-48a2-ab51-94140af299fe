#ifndef MATRIX_HH
#define MATRIX_HH

#include <vector>
#include "Vector.h"

template <typename T, int NumRows, int NumCols>
class Matrix {
 public:
  Matrix();
  Matrix(const T& value);
  explicit Matrix(const std::vector<std::vector<T>>& a);
  Matrix(const Matrix& b);

  T& operator()(int i, int j);
  T operator()(int i, int j) const;
  std::vector<T>& operator[](int i);
  const std::vector<T>& operator[](int i) const;

  Matrix& operator*=(const T& x);
  Matrix& operator+=(const Matrix& b);

  void print() const;
  constexpr int rows() const { return NumRows; };
  constexpr int cols() const { return NumCols; };

 private:
  std::vector<std::vector<T>> entries;
};

template <typename T, int NumRows, int NumCols>
Matrix<T, NumRows, NumCols> operator*(const Matrix<T, NumRows, NumCols>& a,
                                      const T& x);
template <typename T, int NumRows, int NumCols>
Matrix<T, NumRows, NumCols> operator*(const T& x,
                                      const Matrix<T, NumRows, NumCols>& a);
template <typename T, int NumRows, int NumCols>
Vector operator*(const Matrix<T, NumRows, NumCols>& a, const Vector& x);
template <typename T, int NumRows, int NumCols>
std::vector<T> operator*(const Matrix<T, NumRows, NumCols>& a,
                         const std::vector<T>& x);
template <typename T, int NumRows, int NumCols, int OtherCols>
Matrix<T, NumRows, OtherCols> operator*(const Matrix<T, NumRows, NumCols>& a,
                                        const Matrix<T, NumCols, OtherCols>& b);
template <typename T, int NumRows, int NumCols>
Matrix<T, NumRows, NumCols> operator+(const Matrix<T, NumRows, NumCols>& a,
                                      const Matrix<T, NumRows, NumCols>& b);

// Template implementation
#include <cstdlib>
#include <iomanip>
#include <iostream>

template <typename T, int NumRows, int NumCols>
Matrix<T, NumRows, NumCols>::Matrix() : entries(NumRows) {
  for (int i = 0; i < NumRows; ++i)
    entries[i].resize(NumCols);
}

template <typename T, int NumRows, int NumCols>
Matrix<T, NumRows, NumCols>::Matrix(const T& value) : entries(NumRows) {
  for (int i = 0; i < NumRows; ++i) {
    entries[i].resize(NumCols);
    for (int j = 0; j < NumCols; ++j)
      entries[i][j] = value;
  }
}

template <typename T, int NumRows, int NumCols>
Matrix<T, NumRows, NumCols>::Matrix(const std::vector<std::vector<T>>& a) {
  if (a.size() != NumRows) {
    throw std::invalid_argument(
        "Input matrix row count does not match template parameter");
  }
  entries = a;
  for (int i = 0; i < NumRows; ++i) {
    if (entries[i].size() != NumCols) {
      throw std::invalid_argument(
          "Input matrix column count does not match template parameter");
    }
  }
}

template <typename T, int NumRows, int NumCols>
Matrix<T, NumRows, NumCols>::Matrix(const Matrix& b) {
  entries = b.entries;
}

template <typename T, int NumRows, int NumCols>
T& Matrix<T, NumRows, NumCols>::operator()(int i, int j) {
  if (i < 0 || i >= NumRows) {
    std::cerr << "Illegal row index " << i;
    std::cerr << " valid range is [0:" << NumRows - 1 << "]";
    std::cerr << std::endl;
    throw std::out_of_range("Invalid access");
  }
  if (j < 0 || j >= NumCols) {
    std::cerr << "Illegal column index " << j;
    std::cerr << " valid range is [0:" << NumCols - 1 << "]";
    std::cerr << std::endl;
    throw std::out_of_range("Invalid access");
  }
  return entries[i][j];
}

template <typename T, int NumRows, int NumCols>
T Matrix<T, NumRows, NumCols>::operator()(int i, int j) const {
  if (i < 0 || i >= NumRows) {
    std::cerr << "Illegal row index " << i;
    std::cerr << " valid range is [0:" << NumRows - 1 << "]";
    std::cerr << std::endl;
    throw std::out_of_range("Invalid access");
  }
  if (j < 0 || j >= NumCols) {
    std::cerr << "Illegal column index " << j;
    std::cerr << " valid range is [0:" << NumCols - 1 << "]";
    std::cerr << std::endl;
    throw std::out_of_range("Invalid access");
  }
  return entries[i][j];
}

template <typename T, int NumRows, int NumCols>
std::vector<T>& Matrix<T, NumRows, NumCols>::operator[](int i) {
  if (i < 0 || i >= NumRows) {
    std::cerr << "Illegal row index " << i;
    std::cerr << " valid range is [0:" << NumRows - 1 << "]";
    std::cerr << std::endl;
    throw std::out_of_range("Invalid access");
  }
  return entries[i];
}

template <typename T, int NumRows, int NumCols>
const std::vector<T>& Matrix<T, NumRows, NumCols>::operator[](int i) const {
  if (i < 0 || i >= NumRows) {
    std::cerr << "Illegal row index " << i;
    std::cerr << " valid range is [0:" << NumRows - 1 << "]";
    std::cerr << std::endl;
    throw std::out_of_range("Invalid access");
  }
  return entries[i];
}

template <typename T, int NumRows, int NumCols>
Matrix<T, NumRows, NumCols>& Matrix<T, NumRows, NumCols>::operator*=(
    const T& x) {
  for (int i = 0; i < NumRows; ++i)
    for (int j = 0; j < NumCols; ++j)
      entries[i][j] *= x;
  return *this;
}

template <typename T, int NumRows, int NumCols>
Matrix<T, NumRows, NumCols>& Matrix<T, NumRows, NumCols>::operator+=(
    const Matrix& b) {
  // No runtime dimension check needed - template parameters ensure
  // compatibility
  for (int i = 0; i < NumRows; ++i)
    for (int j = 0; j < NumCols; ++j)
      entries[i][j] += b[i][j];
  return *this;
}

template <typename T, int NumRows, int NumCols>
void Matrix<T, NumRows, NumCols>::print() const {
  std::cout << "(" << NumRows << "x";
  std::cout << NumCols << ") matrix:" << std::endl;
  for (int i = 0; i < NumRows; ++i) {
    std::cout << std::setprecision(3);
    for (int j = 0; j < NumCols; ++j)
      std::cout << std::setw(5) << entries[i][j] << " ";
    std::cout << std::endl;
  }
  std::cout << std::endl;
}

// Free function implementations
template <typename T, int NumRows, int NumCols>
Matrix<T, NumRows, NumCols> operator*(const Matrix<T, NumRows, NumCols>& a,
                                      const T& x) {
  Matrix<T, NumRows, NumCols> output(a);
  output *= x;
  return output;
}

template <typename T, int NumRows, int NumCols>
Matrix<T, NumRows, NumCols> operator*(const T& x,
                                      const Matrix<T, NumRows, NumCols>& a) {
  Matrix<T, NumRows, NumCols> output(a);
  output *= x;
  return output;
}

template <typename T, int NumRows, int NumCols>
std::vector<T> operator*(const Matrix<T, NumRows, NumCols>& a,
                         const std::vector<T>& x) {
  if (x.size() != NumCols) {
    std::cerr << "Dimensions of vector " << x.size();
    std::cerr << " and matrix " << NumCols << " do not match!";
    std::cerr << std::endl;
    throw std::invalid_argument("Dimensions do not match");
  }
  std::vector<T> y(NumRows);
  for (int i = 0; i < NumRows; ++i) {
    y[i] = T{};  // Initialize with default value
    for (int j = 0; j < NumCols; ++j)
      y[i] += a[i][j] * x[j];
  }
  return y;
}

template <typename T, int NumRows, int NumCols>
Vector operator*(const Matrix<T, NumRows, NumCols>& a, const Vector& x) {
  // Convert Vector to std::vector<T>, multiply, then convert back
  std::vector<T> vec_entries;
  const auto& entries = x.getEntries();
  vec_entries.reserve(entries.size());
  for (const auto& entry : entries) {
    vec_entries.push_back(static_cast<T>(entry));
  }
  std::vector<T> result = a * vec_entries;
  std::vector<double> double_result;
  double_result.reserve(result.size());
  for (const auto& entry : result) {
    double_result.push_back(static_cast<double>(entry));
  }
  return Vector(double_result);
}

template <typename T, int NumRows, int NumCols>
Matrix<T, NumRows, NumCols> operator+(const Matrix<T, NumRows, NumCols>& a,
                                      const Matrix<T, NumRows, NumCols>& b) {
  Matrix<T, NumRows, NumCols> output(a);
  output += b;
  return output;
}

template <typename T, int NumRows, int NumCols, int OtherCols>
Matrix<T, NumRows, OtherCols> operator*(
    const Matrix<T, NumRows, NumCols>& a,
    const Matrix<T, NumCols, OtherCols>& b) {
  // No runtime dimension check needed - template parameters ensure
  // compatibility
  Matrix<T, NumRows, OtherCols> output;

  for (int i = 0; i < NumRows; ++i)
    for (int j = 0; j < OtherCols; ++j) {
      output(i, j) = T{};
      for (int k = 0; k < NumCols; ++k)
        output(i, j) += a[i][k] * b[k][j];
    }
  return output;
}

#endif  // MATRIX_HH
