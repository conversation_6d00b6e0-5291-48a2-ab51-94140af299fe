#pragma once
#include <ostream>

class Rational {
 public:
  Rational();
  Rational(int num, int denom);
  Rational(int whole_int);

  int numerator() const;
  int denominator() const;

  Rational& operator*=(Rational const& other);
  Rational& operator/=(Rational const& other);
  Rational& operator+=(Rational const& other);
  Rational& operator-=(Rational const& other);
  Rational operator*(Rational const& other);
  Rational operator/(Rational const& other);
  Rational operator+(Rational const& other);
  Rational operator-(Rational const& other);
  bool operator==(Rational const& other);

 private:
  int m_num;
  int m_denom;

  void normalize();
};

int gcd(int a, int b);

// Stream output operator for Rational
std::ostream& operator<<(std::ostream& str, const Rational& r);