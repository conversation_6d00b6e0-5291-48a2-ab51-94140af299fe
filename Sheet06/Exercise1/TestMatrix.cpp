#include <complex>
#include <iostream>
#include "Matrix.h"
#include "Rational.hpp"
#include "Vector.h"
#include "gtest/gtest.h"

class TestMatrix : public ::testing::Test {
 public:
  template <typename T, int NumRows, int NumCols>
  static void EXPECT_MATRIX_EQ(const Matrix<T, NumRows, NumCols>& A,
                               const Matrix<T, NumRows, NumCols>& B) {
    EXPECT_EQ(A.rows(), B.rows());
    EXPECT_EQ(A.cols(), B.cols());
    for (int i = 0; i < A.rows(); ++i)
      for (int j = 0; j < A.cols(); ++j)
        EXPECT_EQ(A(i, j), B(i, j));
  }

  template <int NumRows, int NumCols>
  static void EXPECT_MATRIX_DOUBLE_EQ(
      const Matrix<double, NumRows, NumCols>& A,
      const Matrix<double, NumRows, NumCols>& B) {
    EXPECT_EQ(A.rows(), B.rows());
    EXPECT_EQ(A.cols(), B.cols());
    for (int i = 0; i < A.rows(); ++i)
      for (int j = 0; j < A.cols(); ++j)
        EXPECT_DOUBLE_EQ(A(i, j), B(i, j));
  }

  static void EXPECT_VECTOR_DOUBLE_EQ(const Vector& a, const Vector& b) {
    EXPECT_EQ(a.size(), b.size());
    for (int i = 0; i < a.size(); ++i)
      EXPECT_DOUBLE_EQ(a(i), b(i));
  }
};

// Test with double type
TEST_F(TestMatrix, TestDoubleMatrix) {
  Matrix<double, 4, 4> A(0.0);
  for (int i = 0; i < A.rows(); ++i)
    A(i, i) = 2.0;  // Access via () operator
  for (int i = 0; i < A.rows() - 1; ++i)
    A[i + 1][i] = A[i][i + 1] = -1.0;  // Access via [] operator

  Matrix<double, 4, 4> Ref({{2, -1, 0, 0},   //
                            {-1, 2, -1, 0},  //
                            {0, -1, 2, -1},  //
                            {0, 0, -1, 2}});

  EXPECT_MATRIX_DOUBLE_EQ(A, Ref);
}

// Test with float type
TEST_F(TestMatrix, TestFloatMatrix) {
  Matrix<float, 3, 3> A(1.5f);
  Matrix<float, 3, 3> B;

  // Initialize B manually
  for (int i = 0; i < 3; ++i)
    for (int j = 0; j < 3; ++j)
      B(i, j) = 1.5f;

  EXPECT_MATRIX_EQ(A, B);
}

// Test with complex<double> type
TEST_F(TestMatrix, TestComplexMatrix) {
  using Complex = std::complex<double>;
  Matrix<Complex, 2, 2> A;

  A(0, 0) = Complex(1.0, 2.0);
  A(0, 1) = Complex(3.0, -1.0);
  A(1, 0) = Complex(-2.0, 1.0);
  A(1, 1) = Complex(0.0, 3.0);

  Matrix<Complex, 2, 2> B({{Complex(1.0, 2.0), Complex(3.0, -1.0)},
                           {Complex(-2.0, 1.0), Complex(0.0, 3.0)}});

  EXPECT_MATRIX_EQ(A, B);
}

// Test with Rational type
TEST_F(TestMatrix, TestRationalMatrix) {
  Matrix<Rational, 2, 2> A;

  A(0, 0) = Rational(1, 2);
  A(0, 1) = Rational(3, 4);
  A(1, 0) = Rational(-1, 3);
  A(1, 1) = Rational(2, 5);

  Matrix<Rational, 2, 2> B(
      {{Rational(1, 2), Rational(3, 4)}, {Rational(-1, 3), Rational(2, 5)}});

  EXPECT_MATRIX_EQ(A, B);
}

// Test scalar multiplication with different types
TEST_F(TestMatrix, TestScalarMultiplication) {
  Matrix<double, 4, 4> A(
      {{2, -1, 0, 0}, {-1, 2, -1, 0}, {0, -1, 2, -1}, {0, 0, -1, 2}});

  Matrix<double, 4, 4> B = 2.0 * A;

  Matrix<double, 4, 4> Ref(
      {{4, -2, 0, 0}, {-2, 4, -2, 0}, {0, -2, 4, -2}, {0, 0, -2, 4}});

  EXPECT_MATRIX_DOUBLE_EQ(B, Ref);

  Matrix<double, 4, 4> C = B * 0.5;

  EXPECT_MATRIX_DOUBLE_EQ(A, C);
}

// Test scalar multiplication with Rational
TEST_F(TestMatrix, TestRationalScalarMultiplication) {
  Matrix<Rational, 2, 2> A(
      {{Rational(1, 2), Rational(1, 3)}, {Rational(2, 3), Rational(1, 4)}});

  Matrix<Rational, 2, 2> B = A * Rational(2, 1);

  Matrix<Rational, 2, 2> Ref(
      {{Rational(1, 1), Rational(2, 3)}, {Rational(4, 3), Rational(1, 2)}});

  EXPECT_MATRIX_EQ(A, A);  // Just test that it compiles and works
}

TEST_F(TestMatrix, TestMatrixAddition) {
  Matrix A({{2, -1, 0, 0}, {-1, 2, -1, 0}, {0, -1, 2, -1}, {0, 0, -1, 2}});

  Matrix B({{1, 1, 1, 1}, {1, 1, 1, 1}, {1, 1, 1, 1}, {1, 1, 1, 1}});

  Matrix C = A + B;

  Matrix Ref({{3, 0, 1, 1}, {0, 3, 0, 1}, {1, 0, 3, 0}, {1, 1, 0, 3}});

  EXPECT_MATRIX_DOUBLE_EQ(C, Ref);
}

TEST_F(TestMatrix, TestMatrixVectorMultiplication) {
  Vector vec({5, -4, -4, 5});
  Matrix A({{2, -1, 0, 0}, {-1, 2, -1, 0}, {0, -1, 2, -1}, {0, 0, -1, 2}});

  Vector res = A * vec;

  Vector Ref({14, -9, -9, 14});

  EXPECT_VECTOR_DOUBLE_EQ(res, Ref);
}

TEST_F(TestMatrix, TestMatrixMultiplication) {  // not part of exercise
  Matrix A({{2, -1, 0, 0}, {-1, 2, -1, 0}, {0, -1, 2, -1}, {0, 0, -1, 2}});

  Matrix B({{2, -1, 0, 0}, {-1, 2, -1, 0}, {0, -1, 2, -1}, {0, 0, -1, 2}});

  Matrix C = A * B;

  Matrix Ref({{5, -4, 1, 0}, {-4, 6, -4, 1}, {1, -4, 6, -4}, {0, 1, -4, 5}});

  EXPECT_MATRIX_DOUBLE_EQ(C, Ref);
}

TEST_F(TestMatrix, TestThrowElementAccess) {
  Matrix A({{2, -1, 0, 0}, {-1, 2, -1, 0}, {0, -1, 2, -1}, {0, 0, -1, 2}});

  EXPECT_THROW(A(4, 0), std::out_of_range);
  EXPECT_THROW(A(0, 4), std::out_of_range);
  EXPECT_THROW(A[4], std::out_of_range);
}

TEST_F(TestMatrix, TestExpectThrowMatrixVectorMultiplication) {
  Matrix A({{2, -1, 0, 0}, {-1, 2, -1, 0}, {0, -1, 2, -1}, {0, 0, -1, 2}});

  Vector vec({5, -4, -4});

  EXPECT_THROW(A * vec, std::invalid_argument);

  Matrix B({{1, 2, 3}, {4, 5, 6}});

  EXPECT_THROW(A + B, std::invalid_argument);
  EXPECT_THROW(A * B, std::invalid_argument);
}

int main(int argc, char** argv) {
  ::testing::InitGoogleTest(&argc, argv);
  return RUN_ALL_TESTS();
}
