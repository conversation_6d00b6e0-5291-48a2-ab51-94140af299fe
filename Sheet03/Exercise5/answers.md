> (a) You are writing a finite difference discretization of the Laplace equation, and you are using a
structured grid. For each grid node, the solution at this point is stored. All nodes of the grid are
numbered consecutively and their number is known a priori.

`std::array<T, N>`, where `N` is the number of nodes. 

This looks like a perfect scenario for C-like arrays, where all nodes will be sequentially in memory in continuous memory addresses and I can access a specific node in const time just by knowing its index - and std::array is exactly that. As I know their number in advance, no need to use std::vector.

> (b) Now, you are writing an adaptive finite element discretization for the Laplace equation, and you
are using an unstructured grid. For each grid node, the solution at this point is stored. All nodes
of the grid have a fixed location and have an assigned solution value.

`std::unordered_map` 

For this algorithm I would need to be able to access a value knowing its location, but not all possible locations have a node. Their locations are unstructured, so a hashmap is great where I do not need keys to be sorted, hence unordered_map works well.

> (c) You are writing a Quicksort sorting algorithm, and you want to store the pivot elements in a
stack structure to avoid recursive function calls.

- For pivots: `std::stack<std::pair<int, int>>`. I choose a stack because I want LIFO to simulate a stack. In this stack I want to be able to store ranges of the partitions (first element and last element index in the partition), so I would use a pair.
- For data: `std::vector<T>`, because I want to access elements by index and I do not know size at compile time.

> In the course of this algorithm elements
have to be buffered in a FIFO (first-in, first-out) data structure. The order of the elements in
this FIFO does not change.

`std::queue`, because we need FIFO, and it implements just that, and the order does not change!

> (e) In your finite difference program, the solution is defined a priori on a part of the boundary.
Nodes in this part of the boundary (Dirichlet nodes) aren’t real degrees of freedom and must be
treated differently when assembling the finite difference matrix. The number of these nodes is
small compared to the total number of nodes.
You want to dynamically read the list of Dirichlet nodes and their solution values from a confi-
guration file and make them accessible node by node.

`std::unordered_map<int, T>` where int are indices and T is the type of values. I would use it because I would need to fastly check if current node's index is a Dirichlet node, and if so, to get the value. 

