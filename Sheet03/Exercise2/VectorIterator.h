#pragma once
#include "Vector.h"

class VectorIterator {
    Vector &m_vector;
    size_t m_index;

public:
    explicit VectorIterator(Vector &vector, size_t index);

    VectorIterator &operator++();
    VectorIterator &operator--();
    double &operator*() const;
    bool operator!=(const VectorIterator &other) const;
};

class ConstVectorIterator {
    const Vector &m_vector;
    size_t m_index;

public:
    explicit ConstVectorIterator(const Vector &vector, size_t index);

    ConstVectorIterator &operator++();
    ConstVectorIterator &operator--();
    double operator*() const;
    bool operator!=(const ConstVectorIterator &other) const;
};

VectorIterator begin(Vector &vector);
VectorIterator end(Vector &vector);
ConstVectorIterator cbegin(const Vector &vector);
ConstVectorIterator cend(const Vector &vector);
