#ifndef VECTOR_HH
#define VECTOR_HH

#include <vector>

class Vector {
public:
  explicit Vector(size_t size) : m_entries(size), m_size(size) {};

  explicit Vector(const std::vector<double> &v) {
    m_entries = v;
    m_size = v.size();
  }

  Vector(size_t size, double value) { resize(size, value); };

  Vector(const Vector &v) {
    m_entries = v.m_entries;
    m_size = v.m_size;
  }

  void resize(size_t size, double value);

  void resize(size_t size);

  double &operator()(size_t i);

  const double &operator()(size_t i) const;

  double &operator[](size_t i);

  double operator[](size_t i) const;

  Vector &operator*=(double x);

  Vector &operator+=(const Vector &b);

  const std::vector<double> &getEntries() const;

  size_t size() const { return m_size; }

  void print() const;

private:
  std::vector<double> m_entries;
  size_t m_size = 0;
};

Vector operator+(const Vector &a, const Vector &b);
Vector operator*(const Vector &a, double x);
Vector operator*(double x, const Vector &a);
double operator*(const Vector &a, const Vector &b);

#endif // VECTOR_HH
