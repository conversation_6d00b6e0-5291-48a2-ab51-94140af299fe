#include "VectorIterator.h"

VectorIterator::VectorIterator(Vector &vector, size_t index) : m_vector(vector), m_index(index) {}

VectorIterator &VectorIterator::operator++() {
    ++m_index;
    return *this;
}

VectorIterator &VectorIterator::operator--() {
    --m_index;
    return *this;
}

double &VectorIterator::operator*() const {
    return m_vector[m_index];
}

bool VectorIterator::operator!=(const VectorIterator &other) const {
    return m_index != other.m_index;
}

ConstVectorIterator::ConstVectorIterator(const Vector &vector, size_t index) : m_vector(vector), m_index(index) {}

ConstVectorIterator &ConstVectorIterator::operator++() {
    ++m_index;
    return *this;
}

ConstVectorIterator &ConstVectorIterator::operator--() {
    --m_index;
    return *this;
}

double ConstVectorIterator::operator*() const {
    return m_vector[m_index];
}

bool ConstVectorIterator::operator!=(const ConstVectorIterator &other) const {
    return m_index != other.m_index;
}

VectorIterator begin(Vector &vector) {
    return VectorIterator(vector, 0);
}

VectorIterator end(Vector &vector) {
    return VectorIterator(vector, vector.size());
}

ConstVectorIterator cbegin(const Vector &vector) {
    return ConstVectorIterator(vector, 0);
}

ConstVectorIterator cend(const Vector &vector) {
    return ConstVectorIterator(vector, vector.size());
}