#include "VectorIterator.h"
#include "Vector.h"
#include <gtest/gtest.h>

class TestVectorIteration : public ::testing::Test {
public:
  const double FST_REF_VALUE = 4.0;
  const double SEC_REF_VALUE = 5.0;

  Vector example;

  TestVectorIteration() : example(5, FST_REF_VALUE) {}
};

TEST_F(TestVectorIteration, TestIteration) {
  for (auto it = begin(example); it != end(example); ++it) {
    EXPECT_EQ(*it, FST_REF_VALUE);
    *it = SEC_REF_VALUE;
    EXPECT_EQ(*it, SEC_REF_VALUE);
  }

  for (auto it = cbegin(example); it != cend(example); ++it) {
    EXPECT_EQ(*it, SEC_REF_VALUE);
//        *it = FST_REF_VALUE; should not compile
  }
}

TEST_F(TestVectorIteration, TestRangeBasedLoop) {
  for (const auto &value : example) {
    EXPECT_EQ(value, 4.0);
//        value = 5.0; // should not compile
  }
  for (auto &value : Vector(5, 4.0)) {
    EXPECT_EQ(value, 4.0);
    value = 5.0;
  }
}

int main() {
  testing::InitGoogleTest();
  return RUN_ALL_TESTS();
}