### Try replacing the raw pointers with std::unique_ptr<Node>. What exactly is problematic about this in the linked list?

The issue is that a unique_pointer cannot be copied. The ownership would have to be moved with std::move for each step. If you try to copy the LinkedList, the code will fail, because
the compiler will not use the default copy constructor (unique_ptr is a move-only type).

### Replace the raw pointers with std::shared_ptr<Node>. What happens if we copy the list, and then modify one of the copies? What happens if one list or both go out of scope?

A copy of the list is just a shallow copy. If one of the existing nodes is modified, the other list will also be modified (inplace). But if a node is erased or appended, the other list will not be affected. If one list goes out of scope, the reference count of the shared_ptr will be decremented, but the other list still remains valid. 

### Discuss the benefits and drawbacks of smart pointers for this application. How would you personally implement a doubly linked list, and what would be your design decisions?

In principle, smart pointers are a nice way to have automatic memory management. In this case, the unique_ptr is not a good choice, because copying the list or nodes is not possible (exclusive ownership). I think that shared_ptr is a good choice for the next_ pointer, and weak_ptr for the prev_ pointer. This way ciruclar references are avoided, and the memory management is handled automatically. It is important to keep in mind that a deep copy of a list is only possible with a custom copy constructor.