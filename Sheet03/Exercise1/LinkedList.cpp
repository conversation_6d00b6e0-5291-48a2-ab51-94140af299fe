#include "LinkedList.hpp"
#include <iostream>
#include <stdexcept>

std::shared_ptr<Node> LinkedList::first() const {
  return firstNode_;
}

std::shared_ptr<Node> LinkedList::next(const std::shared_ptr<Node> n) const {
  return n->next_;
}

std::weak_ptr<Node> LinkedList::prev(const std::shared_ptr<Node> n) const {
  return n->prev_;
}

void LinkedList::append(int i) {
  std::shared_ptr<Node> newNode =
      std::make_shared<Node>(i, nullptr, std::weak_ptr<Node>());
  if (firstNode_ == nullptr) {
    firstNode_ = newNode;
  } else {
    std::shared_ptr<Node> currentNode = firstNode_;
    while (currentNode->next_ != nullptr) {
      currentNode = currentNode->next_;
    }
    currentNode->next_ = newNode;
    newNode->prev_ = currentNode;
  }
  maxValid_ = false;
}

void LinkedList::insert(std::shared_ptr<Node> n, int i) {
  std::shared_ptr<Node> newNode =
      std::make_shared<Node>(i, nullptr, std::weak_ptr<Node>());

  if (firstNode_ == nullptr) {
    firstNode_ = newNode;
    maxValid_ = false;
    return;
  }

  std::shared_ptr<Node> currentNode = firstNode_;
  while (currentNode != nullptr && currentNode != n) {
    currentNode = currentNode->next_;
  }

  if (currentNode == n) {
    std::cout << "Node n found, inserting" << std::endl;
    newNode->prev_ = currentNode->prev_;
    newNode->next_ = currentNode;

    if (std::shared_ptr<Node> pred =
            newNode->prev_.lock()) {  // .lock() checks if the object still
                                      // exists and tries to return a shared_ptr
                                      // that shares ownership
      pred->next_ = newNode;
    } else {
      firstNode_ = newNode;
    }
    currentNode->prev_ = newNode;
  } else {
    std::cout << "Node n not found, inserting at the end" << std::endl;
    auto tail = firstNode_;
    while (tail->next_ != nullptr) {
      tail = tail->next_;
      tail->next_ = newNode;
      newNode->prev_ = tail;
    }
  }
  maxValid_ = false;
}

void LinkedList::erase(std::shared_ptr<Node> n) {
  if (!firstNode_ || !n) {
    return;
  }
  if (firstNode_ == n) {
    firstNode_ = firstNode_->next_;
    if (firstNode_)
      firstNode_->prev_.reset();
    maxValid_ = false;
    return;
  }

  std::shared_ptr currentNode = firstNode_;
  while (currentNode->next_ && currentNode->next_ != n) {
    currentNode = currentNode->next_;
  }

  if (currentNode->next_ == n) {
    auto toDelete = currentNode->next_;
    currentNode->next_ = toDelete->next_;
    if (currentNode->next_) {
      currentNode->next_->prev_ = currentNode;
    }
    maxValid_ = false;
  }
}

bool LinkedList::operator==(const LinkedList& other) const {
  std::shared_ptr<Node> iter = first();
  std::shared_ptr<Node> iterOther = other.first();
  while (iter != nullptr && iterOther != nullptr &&
         iter->value_ == iterOther->value_) {
    iter = iter->next_;
    iterOther = iterOther->next_;
  }
  return iter == nullptr && iterOther == nullptr;
}

int LinkedList::max() const {
  if (firstNode_ == nullptr) {
    throw std::runtime_error("Linked list is empty");
  }
  if (!maxValid_) {
    int m = firstNode_->value_;
    for (std::shared_ptr<Node> ptr = firstNode_->next_; ptr; ptr = ptr->next_) {
      m = std::max(m, ptr->value_);
    }
    maxCache_ = m;
    maxValid_ = true;
  }
  return maxCache_;
}

bool LinkedList::isValid() const {
  return maxValid_;
}

void printList(const LinkedList& list) {
  for (std::shared_ptr<Node> n = list.first(); n != 0; n = list.next(n))
    std::cout << n->value_ << std::endl;
  std::cout << "max: " << list.max() << std::endl;
}