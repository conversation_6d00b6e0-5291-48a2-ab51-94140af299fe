#pragma once
#include <iostream>
#include <memory>
#include "node.hpp"

class LinkedList {
 public:
  LinkedList() = default;
  ~LinkedList() = default;
  std::shared_ptr<Node> first() const;
  std::shared_ptr<Node> next(const std::shared_ptr<Node> n) const;
  std::weak_ptr<Node> prev(const std::shared_ptr<Node> n) const;
  void append(int i);
  void insert(std::shared_ptr<Node> n, int i);
  void erase(std::shared_ptr<Node> n);
  bool operator==(const LinkedList& other) const;
  int max() const;
  bool isValid() const;

 private:
  std::shared_ptr<Node> firstNode_{nullptr};
  mutable bool maxValid_ = false;
  mutable int maxCache_;
};

void printList(const LinkedList& list);