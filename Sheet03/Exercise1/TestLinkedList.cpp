#include "LinkedList.hpp"
#include "gtest/gtest.h"

TEST(TestLinkedList, TestEqualOperator) {
  LinkedList list1;
  list1.append(2);
  list1.append(3);

  LinkedList list2;
  list2.append(2);
  list2.append(3);

  EXPECT_TRUE(list1 == list2);
}

TEST(TestLinkedList, TestInsertStart) {
  LinkedList list;
  list.append(2);
  list.append(3);

  list.insert(list.first(), 10);

  LinkedList expectedList;
  expectedList.append(10);
  expectedList.append(2);
  expectedList.append(3);

  EXPECT_TRUE(list == expectedList);
}

TEST(TestLinkedList, TestInsertMiddel) {
  LinkedList list;
  list.append(2);
  list.append(3);

  list.insert(list.next(list.first()), 1);

  LinkedList expectedList;
  expectedList.append(2);
  expectedList.append(1);
  expectedList.append(3);

  EXPECT_TRUE(list == expectedList);
}

TEST(TestLinkedList, TestEraseFront) {
  LinkedList list;
  list.append(2);
  list.append(3);

  list.erase(list.first());

  LinkedList expectedList;
  expectedList.append(3);

  EXPECT_TRUE(list == expectedList);
}

TEST(TestLinkedList, TestMax) {
  LinkedList list;
  list.append(3);
  list.append(5);
  list.append(2);
  list.insert(list.next(list.first()), 1);

  int maxValue = list.max();
  bool isMaxValid = list.isValid();

  EXPECT_EQ(maxValue, 5);
  EXPECT_EQ(isMaxValid, true);
}

TEST(LinkedListTest, PrintListOutputsCorrectly) {
  LinkedList list;
  list.append(7);
  list.append(3);
  list.append(9);

  testing::internal::CaptureStdout();
  printList(list);
  std::string output = testing::internal::GetCapturedStdout();

  std::ostringstream expected;
  expected << "7\n";
  expected << "3\n";
  expected << "9\n";
  expected << "max: 9\n";

  EXPECT_EQ(output, expected.str());
}

int main() {
  ::testing::InitGoogleTest();
  return RUN_ALL_TESTS();
}