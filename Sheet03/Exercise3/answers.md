## a)

The `friend` keyword allows other functions or methods to access private members of a struct or class. This can be
useful
for test classes or as a temporary solution during refactoring.

## b)

`typedef` is used to create an alias for a data type. This can be useful for long types, for example from template
instantiations.

## c)

A `std::shared_ptr` provides memory management where multiple smart pointers can share ownership of an object. It keeps
track of how many pointers share the resource and keeps the object alive until all the shared pointers owning it
are destroyed.

## d)

`std::vector<int> vector = {1, 2, 3, 4, 5};` Creates a `std::vector` of integers named vector, initialized with values 1
through 5.

`auto square = []( auto x ) { return x * x ; };` Creates a lambda function named `square` that takes any type and
returns its square. `[]` means that no variables are being captured.

`[& square ]( const std :: vector <int > & vector ) {
int sum = 0;
for ( const auto & element : vector )
sum += square ( element ) ;
return sum ;
}` Creates a lambda function that takes an int vector and returns the sum of the squares of all the elements in the
vector. `[&square]` means that the square lambda function is captured by reference.

`int result = ...(vector);` Calls the just created lambda function, with `vector` as argument and sets `result` to the
result of the function.

## e)

- `uptr2 = 42;` Cannot assign an int to a unique pointer, the pointer would need to be dereferenced for it to work.
- `auto uptr3 = std::make_unique<double>(new double (42))` Cannot pass a pointer to std::make_unique, should be an
  object instead.
- `auto uptr4 = uptr;` A unique pointer cannot be copied.
- `uptr = std::move(sptr2);` Cannot assign a shared pointer to a unique pointer.