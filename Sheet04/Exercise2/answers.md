## a)

The struct `LotkaVolterraParameters` holds all the parameters of the model, including the initial quantities of the prey
and predator. The class `PopulationData` stores the results of the simulation, so all $X_n$, $Y_n$ values and the
corresponding time steps. It also provides a function to print the simulation results. The class `PredatorPreySystem` is
initialized with the model parameters and contains a function to run the simulation.

## d)

One way is to define an `enum` that specifies the simulation method and pass it as a parameter to the `Simulate`
function. Inside the function, an if statement can be used to compute the results according to the selected method.

Another way is to use inheritance by creating an abstract base class with a virtual `Simulate` method. Each simulation
method would then be implemented in a separate class that derives from this base class.