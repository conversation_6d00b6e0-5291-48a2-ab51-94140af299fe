#include <iostream>
#include <vector>
#include <gtest/gtest_prod.h>

struct LotkaVolterraParameters {
    double alpha, beta;
    double gamma, delta;
    double X0, Y0;
};

class PopulationData {
    std::vector<double> time;
    std::vector<double> prey;
    std::vector<double> predator;

    friend class PredatorPreySystem;
    FRIEND_TEST(TestPredatorPreySimulation, TestEulerSimulation);
    FRIEND_TEST(TestPredatorPreySimulation, TestHeunSimulation);

public :
    explicit PopulationData(int steps)
        : time(steps), prey(steps), predator(steps) {
    }

    void Print() const {
        for (auto n = 0; n < time.size(); ++n)
            std::cout << "Time: " << time[n] << " Prey: " << prey[n] << " Predator: " << predator[n] << std::endl;
    }
};

enum class SimulationMethod {
    Euler,
    Heun
};

class PredatorPreySystem {
    LotkaVolterraParameters params;

public :
    explicit PredatorPreySystem(const LotkaVolterraParameters &description)
        : params(description) {
    }

    PopulationData Simulate(int steps, double dt, SimulationMethod method) const {
        double X = params.X0;
        double Y = params.Y0;
        PopulationData data(steps);

        for (int n = 0; n < steps; ++n) {
            if (method == SimulationMethod::Euler) {
                X += dt * (params.alpha * X - params.beta * X * Y);
                Y += dt * (params.delta * X * Y - params.gamma * Y);
            } else if (method == SimulationMethod::Heun) {
                double k1X = params.alpha * X - params.beta * X * Y;
                double k1Y = params.delta * X * Y - params.gamma * Y;

                double nextX = X + dt * k1X;
                double nextY = Y + dt * k1Y;

                double k2X = params.alpha * nextX - params.beta * nextX * nextY;
                double k2Y = params.delta * nextX * nextY - params.gamma * nextY;

                X += dt / 2 * (k1X + k2X);
                Y += dt / 2 * (k1Y + k2Y);
            }

            data.time[n] = (n + 1) * dt;
            data.prey[n] = X;
            data.predator[n] = Y;
        }

        return data;
    }
};
