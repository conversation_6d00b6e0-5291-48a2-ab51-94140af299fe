#include <gtest/gtest.h>
#include "PredatorPreySimulation.cpp"

TEST(TestPredatorPreySimulation, TestEulerSimulation) {
    PredatorPreySystem system({0.1, 0.02, 0.1, 0.01, 40.0, 9.0});
    auto populationData = system.Simulate(1000, 0.1, SimulationMethod::Euler);

    EXPECT_NEAR(populationData.prey[999], 0.870851, 1e-5);
    EXPECT_NEAR(populationData.predator[999], 9.54076, 1e-5);
}

TEST(TestPredatorPreySimulation, TestHeunSimulation) {
    PredatorPreySystem system({0.1, 0.02, 0.1, 0.01, 40.0, 9.0});
    auto populationData = system.Simulate(1000, 0.1, SimulationMethod::Heun);

    EXPECT_NEAR(populationData.prey[999], 0.874997, 1e-5);
    EXPECT_NEAR(populationData.predator[999], 9.95045, 1e-5);
}

int main() {
    ::testing::InitGoogleTest();
    return RUN_ALL_TESTS();
}