#include "Vector.h"
#include <cstdlib>
#include <iomanip>
#include <iostream>

void Vector::resize(size_t size) {
  m_entries.resize(size);
  m_size = size;
}

void Vector::resize(size_t size, double value) {
  m_entries.resize(size);
  for (double& entry : m_entries) {
    entry = value;
  }
  m_size = size;
}

double& Vector::operator()(size_t i) {
  if (i >= m_size) {
    std::cerr << "Illegal index " << i;
    std::cerr << " valid range is [0:" << m_size - 1 << "]";
    std::cerr << std::endl;
    exit(EXIT_FAILURE);
  }
  return m_entries[i];
}

const double& Vector::operator()(size_t i) const {
  if (i >= m_size) {
    std::cerr << "Illegal index " << i;
    std::cerr << " valid range is [0:" << m_size - 1 << "]";
    std::cerr << std::endl;
    exit(EXIT_FAILURE);
  }
  return m_entries[i];
}

double& Vector::operator[](size_t i) {
  if (i >= m_size) {
    std::cerr << "Illegal index " << i;
    std::cerr << " valid range is [0:" << m_size - 1 << "]";
    std::cerr << std::endl;
    exit(EXIT_FAILURE);
  }
  return m_entries[i];
}

double Vector::operator[](size_t i) const {
  if (i >= m_size) {
    std::cerr << "Illegal index " << i;
    std::cerr << " valid range is [0:" << m_size - 1 << "]";
    std::cerr << std::endl;
    exit(EXIT_FAILURE);
  }
  return m_entries[i];
}

Vector& Vector::operator*=(double x) {
  for (int i = 0; i < m_size; ++i)
    m_entries[i] *= x;
  return *this;
}

Vector& Vector::operator+=(const Vector& b) {
  if (b.m_size != m_size) {
    std::cerr << "Dimensions of vector a (" << m_size << ") and vector b ("
              << b.m_size << ") do not match!";
    std::cerr << std::endl;
    exit(EXIT_FAILURE);
  }
  for (int i = 0; i < m_size; ++i)
    m_entries[i] += b[i];
  return *this;
}

void Vector::print() const {
  std::cout << "(" << m_size << ") vector:" << std::endl;
  for (int i = 0; i < m_size; ++i) {
    std::cout << std::setprecision(3);
    std::cout << std::setw(5) << m_entries[i] << " ";
  }
  std::cout << std::endl;
}

const std::vector<double>& Vector::getEntries() const {
  return m_entries;
}

Vector operator+(const Vector& a, const Vector& b) {
  Vector output(a);
  output += b;
  return output;
}

Vector operator*(const Vector& v, double x) {
  Vector output(v);
  output *= x;
  return output;
}

Vector operator*(double x, const Vector& v) {
  Vector output(v);
  output *= x;
  return output;
}

double operator*(const Vector& a, const Vector& b) {
  const size_t size = a.size();
  if (b.size() != size) {
    std::cerr << "Dimensions of vector a (" << size << ") and vector b ("
              << b.size() << ") do not match!";
    std::cerr << std::endl;
    exit(EXIT_FAILURE);
  }
  double scalarProduct = 0.;
  for (int i = 0; i < size; ++i)
    scalarProduct += a(i) * b(i);
  return scalarProduct;
}

bool Vector::operator==(const Vector& other) const {
  return m_size == other.m_size && m_entries == other.m_entries;
}
