#include <benchmark/benchmark.h>
#include <random>
#include "flatMatrix.hpp"
#include "matrix.hpp"

static void fillRandom(Matrix& M,
                       std::mt19937& gen,
                       std::uniform_real_distribution<double>& dist) {
  const auto [rows, cols] = M.size();
  for (int i = 0; i < rows; ++i)
    for (int j = 0; j < cols; ++j)
      M(i, j) = dist(gen);
}

static void fillRandom(FlatMatrix& M,
                       std::mt19937& gen,
                       std::uniform_real_distribution<double>& dist) {
  const auto [rows, cols] = M.size();
  for (int i = 0; i < rows * cols; ++i)
    M[i] = dist(gen);
}

static void BM_Add_Matrix(benchmark::State& state) {
  const int n = static_cast<int>(state.range(0));
  const int kSeed = 42;
  std::mt19937 rng(kSeed);
  std::uniform_real_distribution<double> uni(-1.0, 1.0);

  Matrix A(n), B(n), R(n);
  fillRandom(A, rng, uni);
  fillRandom(B, rng, uni);

  for (auto _ : state) {
    benchmark::DoNotOptimize(addTwoMatrices(R, A, B));
    benchmark::ClobberMemory();
  }
}

static void BM_Multiply_Matrix(benchmark::State& state) {
  const int n = static_cast<int>(state.range(0));
  const int kSeed = 42;
  std::mt19937 rng(kSeed);
  std::uniform_real_distribution<double> uni(-1.0, 1.0);

  Matrix A(n), B(n), R(n);
  fillRandom(A, rng, uni);
  fillRandom(B, rng, uni);

  for (auto _ : state) {
    benchmark::DoNotOptimize(multiplyMatrixMatrix(R, A, B));
    benchmark::ClobberMemory();
  }
}

BENCHMARK(BM_Multiply_Matrix)
    ->RangeMultiplier(2)
    ->Range(32, 1024)
    ->Unit(benchmark::kMicrosecond);

BENCHMARK(BM_Add_Matrix)
    ->RangeMultiplier(4)
    ->Range(32, 8192)
    ->Unit(benchmark::kMicrosecond);

static void BM_Add_FlatMatrix(benchmark::State& state) {
  const int n = static_cast<int>(state.range(0));
  const int kSeed = 42;
  std::mt19937 rng(kSeed);
  std::uniform_real_distribution<double> uni(-1.0, 1.0);

  FlatMatrix A(n), B(n), R(n);
  fillRandom(A, rng, uni);
  fillRandom(B, rng, uni);

  for (auto _ : state) {
    benchmark::DoNotOptimize(addTwoFlatMatrices(R, A, B));
    benchmark::ClobberMemory();
  }
}

static void BM_Multiply_FlatMatrix(benchmark::State& state) {
  const int n = static_cast<int>(state.range(0));
  const int kSeed = 42;
  std::mt19937 rng(kSeed);
  std::uniform_real_distribution<double> uni(-1.0, 1.0);

  FlatMatrix A(n), B(n), R(n);
  fillRandom(A, rng, uni);
  fillRandom(B, rng, uni);

  for (auto _ : state) {
    benchmark::DoNotOptimize(multiplyFlatMatrices(R, A, B));
    benchmark::ClobberMemory();
  }
}

BENCHMARK(BM_Multiply_FlatMatrix)
    ->RangeMultiplier(2)
    ->Range(32, 1024)
    ->Unit(benchmark::kMicrosecond);

BENCHMARK(BM_Add_FlatMatrix)
    ->RangeMultiplier(4)
    ->Range(32, 8192)
    ->Unit(benchmark::kMicrosecond);

BENCHMARK_MAIN();