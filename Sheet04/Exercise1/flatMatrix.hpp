#pragma once
#include <vector>

class FlatMatrix {
 public:
  FlatMatrix(std::size_t size, std::vector<double>& vals);
  FlatMatrix(std::size_t numRows,
             std::size_t numCols,
             std::vector<double>& vals);
  FlatMatrix(std::size_t size);
  FlatMatrix(std::size_t numRows, std::size_t numCols);
  ~FlatMatrix() = default;

  std::pair<std::size_t, std::size_t> size() const;
  double& operator()(std::size_t i, std::size_t j);
  double& operator[](std::size_t i);
  const double& operator()(std::size_t i, std::size_t j) const;
  const double& operator[](std::size_t i) const;
  const double* values() const { return values_.data(); }
  double* values() { return values_.data(); }
  bool operator==(const FlatMatrix& other) const;

 private:
  std::size_t numRows_{0};
  std::size_t numCols_{0};
  std::vector<double> values_;
};

FlatMatrix& addTwoFlatMatrices(FlatMatrix& res,
                               const FlatMatrix& a,
                               const FlatMatrix& b);

FlatMatrix& multiplyFlatMatrices(FlatMatrix& res,
                                 const FlatMatrix& a,
                                 const FlatMatrix& b);