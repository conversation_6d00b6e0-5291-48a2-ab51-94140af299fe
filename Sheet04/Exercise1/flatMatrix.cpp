#include "flatMatrix.hpp"
#include <stdexcept>

FlatMatrix::FlatMatrix(std::size_t size, std::vector<double>& vals)
    : numRows_(size), numCols_(size) {
  if (size * size != vals.size()) {
    throw std::invalid_argument("Size and vals dim does not match");
  }
  values_ = std::move(vals);
}

FlatMatrix::FlatMatrix(std::size_t numRows,
                       std::size_t numCols,
                       std::vector<double>& vals)
    : numRows_(numRows), numCols_(numCols), values_(std::move(vals)) {
  if (numRows_ * numCols_ != values_.size()) {
    throw std::invalid_argument("Size and vals dim do not match");
  }
}

FlatMatrix::FlatMatrix(std::size_t numRows, std::size_t numCols)
    : numRows_(numRows), numCols_(numCols), values_(numRows * numCols, 0.0) {}

FlatMatrix::FlatMatrix(std::size_t size)
    : numRows_(size), numCols_(size), values_(size * size, 0.0) {}

std::pair<std::size_t, std::size_t> FlatMatrix::size() const {
  return std::make_pair(numRows_, numCols_);
}

double& FlatMatrix::operator()(std::size_t i, std::size_t j) {
  return values_[i * numCols_ + j];
}

double& FlatMatrix::operator[](std::size_t i) {
  return values_[i];
}

const double& FlatMatrix::operator()(std::size_t i, std::size_t j) const {
  return values_[i * numCols_ + j];
}

const double& FlatMatrix::operator[](std::size_t i) const {
  return values_[i];
}

bool FlatMatrix::operator==(const FlatMatrix& other) const {
  return numRows_ == other.numRows_ && numCols_ == other.numCols_ &&
         values_ == other.values_;
}

FlatMatrix& addTwoFlatMatrices(FlatMatrix& res,
                               const FlatMatrix& A,
                               const FlatMatrix& B) {
  auto [rowsA, colsA] = A.size();
  auto [rowsB, colsB] = B.size();
  auto [rowsR, colsR] = res.size();
  if (rowsA != rowsB || colsA != colsB || rowsA != rowsR || colsA != colsR) {
    throw std::invalid_argument("Matrix dimensions must match");
  }

  double* firstRes = &res[0];
  const double* firstA = &A[0];
  const double* firstB = &B[0];

  for (size_t i = 0; i < rowsA * colsA; ++i) {
    firstRes[i] = firstA[i] + firstB[i];
  }
  return res;
}

FlatMatrix& multiplyFlatMatrices(FlatMatrix& res,
                                 const FlatMatrix& A,
                                 const FlatMatrix& B) {
  const auto [rowsA, colsA] = A.size();
  const auto [rowsB, colsB] = B.size();
  if (colsA != rowsB)
    throw std::invalid_argument("Incompatible matrix sizes for multiplication");

  double* rData = res.values();
  const double* aData = A.values();
  const double* bData = B.values();

  for (std::size_t i = 0; i < rowsA; ++i) {
    std::size_t rowA = i * colsA;
    std::size_t rowC = i * colsB;

    for (std::size_t k = 0; k < colsA; ++k) {
      double aVal = aData[rowA + k];

      std::size_t rowB = k * colsB;
      const double* bRow = bData + rowB;

      double* cRow = rData + rowC;
      for (std::size_t j = 0; j < colsB; ++j) {
        cRow[j] += aVal * bRow[j];
      }
    }
  }

  return res;
}