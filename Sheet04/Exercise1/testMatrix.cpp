#include "gtest/gtest.h"
#include "matrix.hpp"

TEST(TestMatrixSetup, TestRectangle) {
  std::vector<std::vector<double>> values{{1, 2, 3}, {4, 5, 6}};
  Matrix A{2, 3, values};
  EXPECT_EQ(A(0, 0), 1);
  EXPECT_EQ(A(1, 2), 6);
}

TEST(TestMatrixSetup, TestSquare) {
  std::vector<std::vector<double>> values{{1, 2}, {4, 5}};
  Matrix A{2, values};
  EXPECT_EQ(A(0, 0), 1);
  EXPECT_EQ(A(1, 1), 5);
}

TEST(TestMatrixSetup, TestRectangleInvalid) {
  std::vector<std::vector<double>> values{{1, 2, 3}, {4, 5, 6}};
  EXPECT_THROW(Matrix(3, 2, values), std::invalid_argument);
}

TEST(TestMatrixSetup, TestSquareInvalid) {
  std::vector<std::vector<double>> values{{1, 2, 3}, {4, 5, 6}};
  EXPECT_THROW(Matrix(2, values), std::invalid_argument);
}

TEST(TestMatrixSetup, TestIndexOperator) {
  std::vector<std::vector<double>> values{{1, 2}, {4, 5}};
  Matrix A{2, values};
  std::vector<double> vec{1, 2};
  EXPECT_EQ(A[0], vec);
}

class TestFreeFunctionsFixture : public ::testing::Test {
 protected:
  std::vector<std::vector<double>> aValues{{1, 2, 3}, {4, 5, 6}};

  std::vector<std::vector<double>> bValues{{1, 2, 3}, {4, 5, 6}};

  std::vector<std::vector<double>> expectedValues{{2, 4, 6}, {8, 10, 12}};

  Matrix A{2, 3, aValues};
  Matrix B{2, 3, bValues};
  Matrix expected{2, 3, expectedValues};
  double scalar{2};
  Matrix res{2, 3};
  std::vector<double> bVec{1, 2, 3};
  std::vector<double> resultVec{0, 0};
  std::vector<double> expectedVec{14, 32};
  Vector bCVec{bVec};
  Vector resultCVec{resultVec};
  Vector expectedCVec{expectedVec};
};

TEST_F(TestFreeFunctionsFixture, TestAdd) {
  std::vector<std::vector<double>> expectedValues{{2, 4, 6}, {8, 10, 12}};
  Matrix expected{2, 3, expectedValues};
  addTwoMatrices(res, A, B);
  EXPECT_EQ(res, expected);
}

TEST_F(TestFreeFunctionsFixture, TestMultiplyMatrixScalar) {
  multiplyMatrixScalar(res, A, scalar);
  EXPECT_EQ(res, expected);
}

TEST_F(TestFreeFunctionsFixture, TestMultiplyScalarMatrix) {
  multiplyScalarMatrix(res, scalar, A);
  EXPECT_EQ(res, expected);
}

TEST_F(TestFreeFunctionsFixture, TestMultiplyMatrixVector) {
  multiplyMatrixVector(resultVec, A, bVec);
  EXPECT_EQ(resultVec, expectedVec);
}

TEST_F(TestFreeFunctionsFixture, TestMultiplyMatrixCustomVector) {
  multiplyMatrixCustomVector(resultCVec, A, bCVec);
  EXPECT_EQ(resultCVec, expectedCVec);
}

int main() {
  ::testing::InitGoogleTest();
  return RUN_ALL_TESTS();
}