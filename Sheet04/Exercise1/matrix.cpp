#include "matrix.hpp"
#include <stdexcept>

Matrix::Matrix(size_t size, std::vector<std::vector<double>>& vals)
    : numRows_(size), numCols_(size) {
  if (size != vals.size()) {
    throw std::invalid_argument("Size and vals dim does not match");
  }
  for (auto i = 0; i < vals.size(); ++i) {
    if (vals[i].size() != size) {
      throw std::invalid_argument("Size and vals dim does not match");
    }
  }
  values_ = std::move(vals);
}

Matrix::Matrix(size_t numRows,
               size_t numCols,
               std::vector<std::vector<double>>& vals)
    : numRows_(numRows), numCols_(numCols) {
  if (numRows != vals.size()) {
    throw std::invalid_argument("Size and vals dim does not match");
  }
  for (auto i = 0; i < vals.size(); ++i) {
    if (vals[i].size() != numCols) {
      throw std::invalid_argument("Size and vals dim does not match");
    }
  }
  values_ = std::move(vals);
}

Matrix::Matrix(size_t size)
    : numRows_(size),
      numCols_(size),
      values_(size, std::vector<double>(size, 0.0)) {}

Matrix::Matrix(size_t numRows, size_t numCols)
    : numRows_(numRows),
      numCols_(numCols),
      values_(numRows, std::vector<double>(numCols, 0.0)) {}

std::pair<size_t, size_t> Matrix::size() const {
  return std::make_pair(numRows_, numCols_);
}

double& Matrix::operator()(size_t i, size_t j) {
  return values_[i][j];
}

std::vector<double>& Matrix::operator[](size_t i) {
  return values_[i];
}

const double& Matrix::operator()(size_t i, size_t j) const {
  return values_[i][j];
}

const std::vector<double>& Matrix::operator[](size_t i) const {
  return values_[i];
}

bool Matrix::operator==(const Matrix& other) const {
  return numRows_ == other.numRows_ && numCols_ == other.numCols_ &&
         values_ == other.values_;
}

Matrix& multiplyMatrixScalar(Matrix& res,
                             const Matrix& A,
                             const double& scalar) {
  for (size_t i = 0; i < A.size().first; ++i) {
    for (size_t j = 0; j < A.size().second; ++j) {
      res(i, j) = scalar * A(i, j);
    }
  }
  return res;
}

Matrix& multiplyScalarMatrix(Matrix& res,
                             const double& scalar,
                             const Matrix& A) {
  res = multiplyMatrixScalar(res, A, scalar);
  return res;
}

std::vector<double>& multiplyMatrixVector(std::vector<double>& res,
                                          const Matrix& A,
                                          const std::vector<double>& bVec) {
  for (size_t i = 0; i < A.size().first; ++i) {
    for (size_t j = 0; j < A.size().second; ++j) {
      res[i] += A(i, j) * bVec[j];
    }
  }
  return res;
}

Vector& multiplyMatrixCustomVector(Vector& res,
                                   const Matrix& A,
                                   const Vector bCVec) {
  for (size_t i = 0; i < A.size().first; ++i) {
    for (size_t j = 0; j < A.size().second; ++j) {
      res(i) += A(i, j) * bCVec(j);
    }
  }
  return res;
}

Matrix& multiplyMatrixMatrix(Matrix& res, const Matrix& A, const Matrix& B) {
  const auto [rowsA, colsA] = A.size();
  const auto [rowsB, colsB] = B.size();
  if (colsA != rowsB) {
    throw std::invalid_argument("Inner dimensions do not match");
  }

  for (size_t i = 0; i < rowsA; ++i) {
    double* resRow = res[i].data();
    const double* aRow = A[i].data();

    for (size_t k = 0; k < colsA; ++k) {
      double aVal = aRow[k];

      const double* bRow = B[k].data();

      for (size_t j = 0; j < colsB; ++j) {
        resRow[j] += aVal * bRow[j];
      }
    }
  }

  return res;
}

Matrix& addTwoMatrices(Matrix& res, const Matrix& A, const Matrix& B) {
  auto [rowsR, colsR] = res.size();
  auto [rowsA, colsA] = A.size();
  auto [rowsB, colsB] = B.size();
  if (rowsR != rowsA || colsR != colsA || rowsA != rowsB || colsA != colsB)
    throw std::invalid_argument("All matrices must have identical dimensions");

  for (size_t i = 0; i < rowsA; ++i) {
    double* resRow = res[i].data();
    const double* bRow = B[i].data();
    const double* aRow = A[i].data();

    for (size_t j = 0; j < colsA; ++j) {
      resRow[j] = aRow[j] + bRow[j];
    }
  }

  return res;
}