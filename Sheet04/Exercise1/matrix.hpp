#pragma once
#include <vector>
#include "Vector.h"

class Matrix {
 public:
  Matrix(size_t size, std::vector<std::vector<double>>& vals);
  Matrix(size_t numRows,
         size_t numCols,
         std::vector<std::vector<double>>& vals);
  Matrix(size_t size);
  Matrix(size_t numRows, size_t numCols);
  ~Matrix() = default;

  std::pair<size_t, size_t> size() const;
  double& operator()(size_t i, size_t j);
  std::vector<double>& operator[](size_t i);
  const double& operator()(size_t i, size_t j) const;
  const std::vector<double>& operator[](size_t i) const;
  bool operator==(const Matrix& other) const;

 private:
  size_t numRows_{0};
  size_t numCols_{0};
  std::vector<std::vector<double>> values_;
};

Matrix& addTwoMatrices(Matrix& res, const Matrix& A, const Matrix& B);
Matrix& multiplyMatrixScalar(Matrix& res,
                             const Matrix& A,
                             const double& scalar);
Matrix& multiplyScalarMatrix(Matrix& res,
                             const double& scalar,
                             const Matrix& A);
std::vector<double>& multiplyMatrixVector(std::vector<double>& res,
                                          const Matrix& A,
                                          const std::vector<double>& bVec);
Vector& multiplyMatrixCustomVector(Vector& resultCVec,
                                   const Matrix& A,
                                   const Vector bCVec);
Matrix& multiplyMatrixMatrix(Matrix& res, const Matrix& A, const Matrix& B);