## (a)
private: The `private` keyword declares that the member is accessible only within the class itself. It cannot be accessed from outside the class or by a derived class.
protected: The `protected` keyword allows the member to be accessed within the class itself and by a derived class, but not from outside.

## (b)
Virtual destructors should be used whenever polymorphism is involved. It ensures that when the base class pointer is deleted, also the derived class's destructor is called.
A good rule of thumb is to declare a virtual destructor whenever the class hirarchy includes virtual functions.

## (c)
RAII stands for Resource Acquisition Is Initialization. It is a core C++ idiom that ties the lifetime of memory to the lifetime of an object. When an object is created, it acquires resources (like memory), and when the object is destroyed, it releases those resources. This ensures that resources are properly managed when objects go out of scope.

## (d)
In principle, treating the `Square` class as a child of the `Rectangle` class can seem like a good idea because a square is a specific type of a rectangle. However, it can lead to issues because a square has specific constraints (all sides are equal) that do not apply to a general rectangle. This violates the Liskov Substitution Principle, which states that objects of a parent class should be replaceable with objects of a child class without affecting the correctness of the program. The problem becomes clear when you use the setters for width and height of the parent class `Rectangle` on an object of the `Square` class, which would violate the core definition of a square. A better approach is to use composition instead of inheritance, where `Square` can have a `Rectangle` as a member without exposing the setters that would violate its constraints.

## (e)
The choice of using inheritance implies an "is-a" relationship. However, the defined shapes have and not are corners, so it is more appropriate to use composition. The different shapes can be
defined as classes that contain a `Corner` class.