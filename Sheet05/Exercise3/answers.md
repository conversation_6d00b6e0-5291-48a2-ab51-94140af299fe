## Question 1
The program is expected to print the number 2. Because template-parameters of a template template-parameter are permitted to have default values, such default values are then applied to the template template-parameter. This means that when g<X>() is called, it is equivalent to g<X<B>>(). After the scope resolution, the program will print the number 2.

## Question 2
The struct X has a default and copy constructor defined.
X x calls the default constructor -> a ist printed
X y(x) calls the copy constructor -> b is printed
X z = y calls the copy constructor -> b is printed
z = x calls the assignment operator -> c is printed
So the output is: abbc

## Question 3
The class only holds a static member variable, so it is shared across all instances of the class of the same type. So the output will be 112.
f(1) -> 1 is printed
f(1.0) -> is now a double, so it is not the same type as the first call, so it is a new instance of the class, and 1.0 is printed
f(1) -> is now the same type as the first call, so it is the same instance of the class, and 2 is printed

## Question 4
The output is abBA. When an object of the derived class is created, the base class constructor is called first, which prints 'a'. Then the derived class constructor is called, which prints 'b'. When the object goes out of scope, the derived class destructor is called first, which prints 'B', and then the base class destructor is called, which prints 'A'.

## Question 5
true, true, true so 111 is printed. First, m[7] is currently not in the map, so the default constructor of C is called. C(1) calls the other constructor and in the end, the instance of C is assigned to m[7].

Question 5 covers constructors and assignment operators as well as the behavior of maps.