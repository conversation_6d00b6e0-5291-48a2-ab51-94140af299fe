### What would you do differently if you want to implement it more efficiently?

- Avoid calling the function many times for the same points.
- Store precomputed function values if possible.

- Use algorithms that adapt the subinterval size where the function "dances" more.

- Use multiple CPU cores to compute subintervals in parallel.

---

### Where do you need inheritance, and where do you need composition?

- **Inheritance**: to define `Functor` and `QuadratureRule` interfaces via abstract base classes.
- **Composition**: in `CompositeRule`, which holds a reference to a `QuadratureRule` instance.

---

### Can you find a function for which the EOC is not as expected, and if so, what is the reason for this?

Yes, non-smooth functions like `abs(x)`, for which the assumptions for error bounds break down, leading to irregular EOC behavior.

---

### Convergence experiment

We integrated `cos(x)` on `[-π/2, π/2]` with increasing subinterval counts. The exact integral is `2.0`. The following errors were obtained:

| N   | Error (TrapezRule) | Error (SimpsonRule) |
|-----|--------------------|---------------------|
| 2   | 0.429204           | 0.00455975          |
| 4   | 0.103881           | 0.00026917          |
| 8   | 0.0257684          | 1.6591e-05          |
| 16  | 0.00642966         | 1.03337e-06         |
| 32  | 0.00160664         | 6.453e-08           |
| 64  | 0.000401611        | 4.03226e-09         |
| 128 | 0.0001004          | 2.52003e-10         |

The results are shown in the following figure:

![Convergence Plot](Figure_1.png)

Both methods demonstrate the expected convergence orders (O(h²) for trapezoidal and O(h⁴) for Simpson).
