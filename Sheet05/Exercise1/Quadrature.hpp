#pragma once

#include <vector>
#include <cmath>
#include <stdexcept>

//
// Functor interface
//
class Functor {
public:
    virtual double operator()(double x) const = 0;
    virtual ~Functor() = default;
};

//
// Polynomial functor
//
class Polynomial : public Functor {
public:
    explicit Polynomial(const std::vector<double>& coefficients)
        : coeffs_(coefficients) {}

    double operator()(double x) const override {
        double result = 0.0;
        double x_power = 1.0;
        for (double coeff : coeffs_) {
            result += coeff * x_power;
            x_power *= x;
        }
        return result;
    }

private:
    std::vector<double> coeffs_;
};

//
// Cos functor
//
class Cos : public Functor {
public:
    double operator()(double x) const override {
        return std::cos(x);
    }
};

//
// QuadratureRule interface
//
class QuadratureRule {
public:
    virtual double integrate(const Functor& f, double a, double b) const = 0;
    virtual int degreeOfExactness() const = 0;
    virtual double ExpectedOrderOfConvergence() const = 0;
    virtual ~QuadratureRule() = default;
};

//
// TrapezRule
//
class TrapezRule : public QuadratureRule {
public:
    double integrate(const Functor& f, double a, double b) const override {
        return (b - a) * (f(a) + f(b)) / 2.0;
    }

    int degreeOfExactness() const override { return 1; }
    double ExpectedOrderOfConvergence() const override { return 2.0; }
};

//
// SimpsonRule
//
class SimpsonRule : public QuadratureRule {
public:
    double integrate(const Functor& f, double a, double b) const override {
        double m = (a + b) / 2.0;
        return (b - a) / 6.0 * (f(a) + 4.0 * f(m) + f(b));
    }

    int degreeOfExactness() const override { return 3; }
    double ExpectedOrderOfConvergence() const override { return 4.0; }
};

//
// CompositeRule
//
class CompositeRule : public QuadratureRule {
public:
    CompositeRule(const QuadratureRule& baseRule, int numSubIntervals)
        : baseRule_(baseRule), N_(numSubIntervals) {
        if (N_ <= 0) throw std::invalid_argument("Number of subintervals must be positive.");
    }

    double integrate(const Functor& f, double a, double b) const override {
        double result = 0.0;
        double h = (b - a) / static_cast<double>(N_);
        for (int i = 0; i < N_; ++i) {
            double left = a + i * h;
            double right = left + h;
            result += baseRule_.integrate(f, left, right);
        }
        return result;
    }

    int degreeOfExactness() const override {
        return baseRule_.degreeOfExactness();
    }

    double ExpectedOrderOfConvergence() const override {
        return baseRule_.ExpectedOrderOfConvergence();
    }

private:
    const QuadratureRule& baseRule_;
    int N_;
};

//
// EOC helper function
//
inline double EOC(double factor, double EN, double E2N) {
    return std::log(EN / E2N) / std::log(factor);
}
