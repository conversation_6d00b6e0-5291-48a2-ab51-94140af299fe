### a)

Anything in a `class` is `private` unless declared otherwise, and anything in a `struct` is `public` unless declared otherwise.

### b)

Temporaries are unnamed intermediate values that aren't stored in memory. Literals are constant values explicitly written into the code.

### c)

```cpp
void IncrementOverload(int &a, int b = 1) { 
    a += b; 
}
```

### d)

Header guards are preprocessor statements that allow header files to be included only once. They are needed because header files are often included multiple times within a program, but without header guards, this would lead to redefinition errors.

### e)

The code snippet contains a function template that defines the function `Power`. The function template has the compile-time constant `POWER` with the default value `2`, and the type `T`, which is `int` by default. The function `Power` takes a value of type `T` and multiplies the value by itself `POWER - 1` times in a loop. Because a function template is used, the function can be reused for different types and `POWER` values at compile time.
