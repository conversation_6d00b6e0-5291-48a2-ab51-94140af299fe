## 1
```cpp
int j = 1;

int main() {
  int& i = j, j;
  j = 2;
  std::cout << i << j;
}
```
The question is about the lifetime of variables. After a variable is declared, it lives in the scope where it was declared. It starts its lifetime immediately after it was declared and ends its life when the scope ends.

The solution shows that in case of existance of a global variable with the same name as a local variable, the global variable will be used until a local variable is declared.

Solution: `12`

## 2
```cpp
int main() {
    int n = 3;
    int i = 0;

    switch (n % 2) {
    case 0:
    do {
    ++i;
    case 1: ++i;
    } while (--n > 0);
    }

    std::cout << i;
}
```
The construct used is the <PERSON>'s device. It allows to unroll one iteration of a loop and jump into the desired iteration by using the switch command. It works because when cases do not have a break command, the flow of the program continues to the next case.

I would not recommend to use this in real-world code because it is unreadable and will confuse everyone else. Only use it when performance gains are big enough to justify the understandability.

Solution: `5`

## 3
```cpp
int f(int &a, int &b) {
  a = 3;
  b = 4;
  return a + b;
}

int main() {
  int a = 1;
  int b = 2;
  int c = f(a, a);
  std::cout << a << b << c;
}
```
Question is about passing arguments to a function via reference and modifying them, and what happens if the same object is passed twice. In that case the passed object gets two alliases in the scope of the function body and can be referenced using 2 names (aliases).

Solution: `428`

## 4
```cpp
template<typename T>
void f(T) {
    std::cout << 1;
}

template<>
void f(int) {
    std::cout << 2;
}

void f(int) {
    std::cout << 3;
}

int main() {
    f(0.0);
    f(0);
    f<>(0);
}
```
The question is about the priority of function name resolution - which of the functions will be called when they have the same name, i.e. function name is overloaded, in this context with respect to templated functions.

The answer is that the most specific function will be selected - when there exists an explicit function that takes ```int``` as an argument, it will be selected. To explicitly call a template function even when a function with the same argument type in the signature exists, we must explicitly call a template function via ```t<>(0);```.

Solution: `132`

## 5
```cpp
struct A {
  A() { std::cout << "A"; }
};
struct B {
  B() { std::cout << "B"; }
};

class C {
public:
  C() : a(), b() {}

private:
  B b;
  A a;
};

int main()
{
    C();
}
```
The question is about the order of initialization of class variables. The idea is that the order is determined via the order of declaration and not the order of initialization in the constructor.

Solution: `BA`
