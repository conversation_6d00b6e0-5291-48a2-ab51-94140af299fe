#include <gtest/gtest.h>
#include "Rational.hpp"

// Test all constructors

TEST(RationalConstructor, ConstructorStoresValues) {
    Rational r(3, 4);
    EXPECT_EQ(r.numerator(), 3);
    EXPECT_EQ(r.denominator(), 4);
}

TEST(RationalConstructor, Default) {
    Rational r;
    EXPECT_EQ(r.numerator(), 1);
    EXPECT_EQ(r.denominator(), 1);
}

TEST(RationalConstructor, TwoArgAndNormalize) {
    Rational r(2, 4);
    EXPECT_EQ(r.numerator(), 1);
    EXPECT_EQ(r.denominator(), 2);
}

TEST(RationalConstructor, WholeInt) {
    Rational r(5);
    EXPECT_EQ(r.numerator(), 5);
    EXPECT_EQ(r.denominator(), 1);
}

// Test aritmetic operators

/*
For myself: This declaration is used as a fixture and
used in the Google Test framework (inherit from the base Test class).
The fractions are protected and not private, because Google Test produces
subclasses for each TEST_F and like this the fractions can be simply used
in each subclass.
*/
class RationalAritmetic : public ::testing::Test {
    protected:
        Rational f1{-3, 12};
        Rational f2{4, 3};
        Rational f3{0, 1};
    };

TEST_F(RationalAritmetic, UnaryAdd) {
    Rational r;
    r = f1 + f2;
    EXPECT_EQ(r.numerator(), 13);
    EXPECT_EQ(r.denominator(), 12);
}

TEST_F(RationalAritmetic, UnaryMult) {
    Rational r;
    r = f1 * f2;
    EXPECT_EQ(r.numerator(), -1);
    EXPECT_EQ(r.denominator(), 3);
}

TEST_F(RationalAritmetic, ScalarAdd) {
    Rational r;
    Rational int_r(4);
    r = int_r + f2;
    EXPECT_EQ(r.numerator(), 16);
    EXPECT_EQ(r.denominator(), 3);
}

TEST_F(RationalAritmetic, ScalarAddSwitched) {
    Rational r;
    Rational int_r(5);
    r = f2 + int_r;
    EXPECT_EQ(r.numerator(), 19);
    EXPECT_EQ(r.denominator(), 3);
}

TEST_F(RationalAritmetic, ScalarMult) {
    Rational r;
    Rational int_r(12);
    r = int_r * f1;
    EXPECT_EQ(r.numerator(), -3);
    EXPECT_EQ(r.denominator(), 1);
}

TEST_F(RationalAritmetic, ScalarMultSwitched) {
    Rational r;
    Rational int_r(6);
    r = f1 * int_r;
    EXPECT_EQ(r.numerator(), -3);
    EXPECT_EQ(r.denominator(), 2);
}

TEST_F(RationalAritmetic, Division) {
    Rational r;
    r = f1 / f2;
    EXPECT_EQ(r.numerator(), -3);
    EXPECT_EQ(r.denominator(), 16);
}