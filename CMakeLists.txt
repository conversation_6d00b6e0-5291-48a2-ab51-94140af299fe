# Initial setup
#---------------------------------------------------------------------------------------#
cmake_minimum_required(VERSION 3.15)

project(OOP4SC)

set(CMAKE_CXX_STANDARD_REQUIRED 20)
set(CMAKE_CXX_STANDARD 20)
#---------------------------------------------------------------------------------------#

# Google test / Google benchmark
#---------------------------------------------------------------------------------------#
add_subdirectory(googletest)
add_subdirectory(benchmark)
include_directories(googletest/googletest/include)
set(GTEST_LIB gtest gtest_main gmock gmock_main)
enable_testing()

# # Sheet 0
# #---------------------------------------------------------------------------------------#
# add_executable(hello_world
#     Sheet00/Exercise1/hello_world.cpp
# )

# # Sheet 1
# #---------------------------------------------------------------------------------------#
# add_library(RationalLib Sheet01/Exercise1/Rational.cpp)
# add_executable(TestRational Sheet01/Exercise1/TestRational.cpp)
# target_link_libraries(TestRational ${GTEST_LIB} RationalLib)
# add_test(NAME TestRational COMMAND TestRational)

# # Sheet 2
# #---------------------------------------------------------------------------------------#
# add_library(LinkedListWithRawPointersLib Sheet02/Exercise1/LinkedListWithRawPointers.cpp Sheet02/Exercise1/Node.cpp)
# add_executable(TestLinkedListWithRawPointers Sheet02/Exercise1/TestLinkedListWithRawPointers.cpp)
# target_link_libraries(TestLinkedListWithRawPointers ${GTEST_LIB} LinkedListWithRawPointersLib)
# add_test(NAME TestLinkedListWithRawPointers COMMAND TestLinkedListWithRawPointers)

# add_library(LinkedListConstLibLennart Sheet02/Exercise2/LinkedListConst.cpp Sheet02/Exercise2/nodeConst.cpp)
# add_executable(TestLinkedListConstLennart Sheet02/Exercise2/TestLinkedListConst.cpp)
# target_link_libraries(TestLinkedListConstLennart ${GTEST_LIB} LinkedListConstLibLennart)
# add_test(NAME TestLinkedListConstLennart COMMAND TestLinkedListConstLennart)

# # Sheet 3
# #---------------------------------------------------------------------------------------#
# add_library(LinkedListShared Sheet03/Exercise1/LinkedList.cpp Sheet03/Exercise1/node.cpp)
# add_executable(TestLinkedList Sheet03/Exercise1/TestLinkedList.cpp)
# target_link_libraries(TestLinkedList ${GTEST_LIB} LinkedListShared)
# add_test(NAME TestLinkedList COMMAND TestLinkedList)

# add_library(VectorIteratorLib Sheet03/Exercise2/Vector.cpp Sheet03/Exercise2/VectorIterator.cpp)
# add_executable(TestVectorIterator Sheet03/Exercise2/TestVectorIterator.cpp)
# target_link_libraries(TestVectorIterator ${GTEST_LIB} VectorIteratorLib)
# add_test(NAME TestVectorIterator COMMAND TestVectorIterator)

# # Sheet 4
# #---------------------------------------------------------------------------------------#
# add_library(Matrix Sheet04/Exercise1/matrix.cpp Sheet04/Exercise1/Vector.cpp)
# add_executable(TestMatrix Sheet04/Exercise1/testMatrix.cpp)
# target_link_libraries(TestMatrix ${GTEST_LIB} Matrix)
# add_test(NAME TestMatrix COMMAND TestMatrix)

# add_library(PredatorPreySimulation Sheet04/Exercise2/PredatorPreySimulation.cpp)
# add_executable(TestPredatorPreySimulation Sheet04/Exercise2/TestPredatorPreySimulation.cpp)
# target_link_libraries(TestPredatorPreySimulation ${GTEST_LIB} PredatorPreySimulation)
# add_test(NAME TestPredatorPreySimulation COMMAND TestPredatorPreySimulation)

# add_executable(matrix_bench Sheet04/Exercise1/benchmark.cpp
#     Sheet04/Exercise1/matrix.cpp
#     Sheet04/Exercise1/flatMatrix.cpp
#     Sheet04/Exercise1/Vector.cpp
# )
# target_link_libraries(matrix_bench PRIVATE benchmark::benchmark benchmark::benchmark_main)
# target_compile_options(matrix_bench PRIVATE -O3 -march=native -DNDEBUG)

# Sheet 5
#---------------------------------------------------------------------------------------#
add_library(QuadratureLib Sheet05/Exercise1/Quadrature.cpp)
add_executable(TestQuadrature Sheet05/Exercise1/TestQuadratureRules.cpp)
target_link_libraries(TestQuadrature ${GTEST_LIB} QuadratureLib)
add_test(NAME TestQuadrature COMMAND TestQuadrature)

# Sheet 6
#---------------------------------------------------------------------------------------#
add_library(QuadratureLib6 Sheet06/Exercise3/QuadratureCRTP.cpp)
add_executable(TestQuadrature6 Sheet06/Exercise3/TestQuadratureRules.cpp)
target_link_libraries(TestQuadrature6 ${GTEST_LIB} QuadratureLib6)
add_test(NAME TestQuadrature6 COMMAND TestQuadrature6)