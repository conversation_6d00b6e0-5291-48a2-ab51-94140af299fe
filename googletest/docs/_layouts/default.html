<!DOCTYPE html>
<html lang="{{ site.lang | default: "en-US" }}">
  <head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

{% seo %}
    <link rel="stylesheet" href="{{ "/assets/css/style.css?v=" | append: site.github.build_revision | relative_url }}">
    <script>
      window.ga=window.ga||function(){(ga.q=ga.q||[]).push(arguments)};ga.l=+new Date;
      ga('create', 'UA-197576187-1', { 'storage': 'none' });
      ga('set', 'referrer', document.referrer.split('?')[0]);
      ga('set', 'location', window.location.href.split('?')[0]);
      ga('set', 'anonymizeIp', true);
      ga('send', 'pageview');
    </script>
    <script async src='https://www.google-analytics.com/analytics.js'></script>
  </head>
  <body>
    <div class="sidebar">
      <div class="header">
        <h1><a href="{{ "/" | relative_url }}">{{ site.title | default: "Documentation" }}</a></h1>
      </div>
      <input type="checkbox" id="nav-toggle" class="nav-toggle">
      <label for="nav-toggle" class="expander">
        <span class="arrow"></span>
      </label>
      <nav>
        {% for item in site.data.navigation.nav %}
        <h2>{{ item.section }}</h2>
        <ul>
          {% for subitem in item.items %}
          <a href="{{subitem.url | relative_url }}">
            <li class="{% if subitem.url == page.url %}active{% endif %}">
              {{ subitem.title }}
            </li>
          </a>
          {% endfor %}
        </ul>
        {% endfor %}
      </nav>
    </div>
    <div class="main markdown-body">
      <div class="main-inner">
        {{ content }}
      </div>
      <div class="footer">
        GoogleTest &middot;
        <a href="https://github.com/google/googletest">GitHub Repository</a> &middot;
        <a href="https://github.com/google/googletest/blob/main/LICENSE">License</a> &middot;
        <a href="https://policies.google.com/privacy">Privacy Policy</a>
      </div>
    </div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/anchor-js/4.1.0/anchor.min.js" integrity="sha256-lZaRhKri35AyJSypXXs4o6OPFTbTmUoltBbDCbdzegg=" crossorigin="anonymous"></script>
    <script>anchors.add('.main h2, .main h3, .main h4, .main h5, .main h6');</script>
  </body>
</html>
