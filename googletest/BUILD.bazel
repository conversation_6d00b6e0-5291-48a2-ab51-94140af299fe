# Copyright 2017 Google Inc.
# All Rights Reserved.
#
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are
# met:
#
#     * Redistributions of source code must retain the above copyright
# notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above
# copyright notice, this list of conditions and the following disclaimer
# in the documentation and/or other materials provided with the
# distribution.
#     * Neither the name of Google Inc. nor the names of its
# contributors may be used to endorse or promote products derived from
# this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
# "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
# LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
# A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
# OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
# SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
# LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
# DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
# THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
# OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
#
#   Bazel Build for Google C++ Testing Framework(Google Test)

package(default_visibility = ["//visibility:public"])

licenses(["notice"])

exports_files(["LICENSE"])

config_setting(
    name = "qnx",
    constraint_values = ["@platforms//os:qnx"],
)

config_setting(
    name = "windows",
    constraint_values = ["@platforms//os:windows"],
)

config_setting(
    name = "freebsd",
    constraint_values = ["@platforms//os:freebsd"],
)

config_setting(
    name = "openbsd",
    constraint_values = ["@platforms//os:openbsd"],
)

# NOTE: Fuchsia is not an officially supported platform.
config_setting(
    name = "fuchsia",
    constraint_values = ["@platforms//os:fuchsia"],
)

config_setting(
    name = "msvc_compiler",
    flag_values = {
        "@bazel_tools//tools/cpp:compiler": "msvc-cl",
    },
    visibility = [":__subpackages__"],
)

config_setting(
    name = "has_absl",
    values = {"define": "absl=1"},
)

# Library that defines the FRIEND_TEST macro.
cc_library(
    name = "gtest_prod",
    hdrs = ["googletest/include/gtest/gtest_prod.h"],
    includes = ["googletest/include"],
)

# Google Test including Google Mock

# For an actual test, use `gtest` and also `gtest_main` if you depend on gtest's
# main(). For a library, use `gtest_for_library` instead if the library can be
# testonly.
cc_library(
    name = "gtest",
    srcs = glob(
        include = [
            "googletest/src/*.cc",
            "googletest/src/*.h",
            "googletest/include/gtest/**/*.h",
            "googlemock/src/*.cc",
            "googlemock/include/gmock/**/*.h",
        ],
        exclude = [
            "googletest/src/gtest-all.cc",
            "googletest/src/gtest_main.cc",
            "googlemock/src/gmock-all.cc",
            "googlemock/src/gmock_main.cc",
        ],
    ),
    hdrs = glob([
        "googletest/include/gtest/*.h",
        "googlemock/include/gmock/*.h",
    ]),
    copts = select({
        ":qnx": [],
        ":windows": [],
        "//conditions:default": ["-pthread"],
    }),
    defines = select({
        ":has_absl": ["GTEST_HAS_ABSL=1"],
        "//conditions:default": [],
    }),
    features = select({
        ":windows": ["windows_export_all_symbols"],
        "//conditions:default": [],
    }),
    includes = [
        "googlemock",
        "googlemock/include",
        "googletest",
        "googletest/include",
    ],
    linkopts = select({
        ":qnx": ["-lregex"],
        ":windows": [],
        ":freebsd": [
            "-lm",
            "-pthread",
        ],
        ":openbsd": [
            "-lm",
            "-pthread",
        ],
        "//conditions:default": ["-pthread"],
    }),
    deps = select({
        ":has_absl": [
            "@abseil-cpp//absl/container:flat_hash_set",
            "@abseil-cpp//absl/debugging:failure_signal_handler",
            "@abseil-cpp//absl/debugging:stacktrace",
            "@abseil-cpp//absl/debugging:symbolize",
            "@abseil-cpp//absl/flags:flag",
            "@abseil-cpp//absl/flags:parse",
            "@abseil-cpp//absl/flags:reflection",
            "@abseil-cpp//absl/flags:usage",
            "@abseil-cpp//absl/strings",
            "@abseil-cpp//absl/types:any",
            "@abseil-cpp//absl/types:optional",
            "@abseil-cpp//absl/types:variant",
            "@re2//:re2",
        ],
        "//conditions:default": [],
    }) + select({
        # `gtest-death-test.cc` has `EXPECT_DEATH` that spawns a process,
        # expects it to crash and inspects its logs with the given matcher,
        # so that's why these libraries are needed.
        # Otherwise, builds targeting Fuchsia would fail to compile.
        ":fuchsia": [
            "@fuchsia_sdk//pkg/fdio",
            "@fuchsia_sdk//pkg/syslog",
            "@fuchsia_sdk//pkg/zx",
        ],
        "//conditions:default": [],
    }),
)

# `gtest`, but testonly. See guidance on `gtest` for when to use this.
alias(
    name = "gtest_for_library",
    actual = ":gtest",
    testonly = True,
)

# Implements main() for tests using gtest. Prefer to depend on `gtest` as well
# to ensure compliance with the layering_check Bazel feature where only the
# direct hdrs values are available.
cc_library(
    name = "gtest_main",
    srcs = ["googlemock/src/gmock_main.cc"],
    features = select({
        ":windows": ["windows_export_all_symbols"],
        "//conditions:default": [],
    }),
    deps = [":gtest"],
)

# The following rules build samples of how to use gTest.
cc_library(
    name = "gtest_sample_lib",
    srcs = [
        "googletest/samples/sample1.cc",
        "googletest/samples/sample2.cc",
        "googletest/samples/sample4.cc",
    ],
    hdrs = [
        "googletest/samples/prime_tables.h",
        "googletest/samples/sample1.h",
        "googletest/samples/sample2.h",
        "googletest/samples/sample3-inl.h",
        "googletest/samples/sample4.h",
    ],
    features = select({
        ":windows": ["windows_export_all_symbols"],
        "//conditions:default": [],
    }),
)

cc_test(
    name = "gtest_samples",
    size = "small",
    # All Samples except:
    #   sample9 (main)
    #   sample10 (main and takes a command line option and needs to be separate)
    srcs = [
        "googletest/samples/sample1_unittest.cc",
        "googletest/samples/sample2_unittest.cc",
        "googletest/samples/sample3_unittest.cc",
        "googletest/samples/sample4_unittest.cc",
        "googletest/samples/sample5_unittest.cc",
        "googletest/samples/sample6_unittest.cc",
        "googletest/samples/sample7_unittest.cc",
        "googletest/samples/sample8_unittest.cc",
    ],
    linkstatic = 0,
    deps = [
        "gtest_sample_lib",
        ":gtest_main",
    ],
)

cc_test(
    name = "sample9_unittest",
    size = "small",
    srcs = ["googletest/samples/sample9_unittest.cc"],
    deps = [":gtest"],
)

cc_test(
    name = "sample10_unittest",
    size = "small",
    srcs = ["googletest/samples/sample10_unittest.cc"],
    deps = [":gtest"],
)
