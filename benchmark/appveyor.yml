version: '{build}'

image: Visual Studio 2017

configuration:
  - Debug
  - Release

environment:
  matrix:
    - compiler: msvc-15-seh
      generator: "Visual Studio 15 2017"

    - compiler: msvc-15-seh
      generator: "Visual Studio 15 2017 Win64"

    - compiler: msvc-14-seh
      generator: "Visual Studio 14 2015"

    - compiler: msvc-14-seh
      generator: "Visual Studio 14 2015 Win64"

    - compiler: gcc-5.3.0-posix
      generator: "MinGW Makefiles"
      cxx_path: 'C:\mingw-w64\i686-5.3.0-posix-dwarf-rt_v4-rev0\mingw32\bin'
      APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2015

matrix:
  fast_finish: true

install:
  # git bash conflicts with MinGW makefiles
  - if "%generator%"=="MinGW Makefiles" (set "PATH=%PATH:C:\Program Files\Git\usr\bin;=%")
  - if not "%cxx_path%"=="" (set "PATH=%PATH%;%cxx_path%")

build_script:
  - md _build -Force
  - cd _build
  - echo %configuration%
  - cmake -G "%generator%" "-DCMAKE_BUILD_TYPE=%configuration%" -DBENCHMARK_DOWNLOAD_DEPENDENCIES=ON ..
  - cmake --build . --config %configuration%

test_script:
  - ctest --build-config %configuration% --timeout 300 --output-on-failure

artifacts:
  - path: '_build/CMakeFiles/*.log'
    name: logs
  - path: '_build/Testing/**/*.xml'
    name: test_results
