name: build-and-test-perfcounters

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

env:
  CMAKE_GENERATOR: Ninja

jobs:
  job:
    # TODO(dominic): Extend this to include compiler and set through env: CC/CXX.
    name: ${{ matrix.os }}.${{ matrix.build_type }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest]
        build_type: ['Release', 'Debug']
    steps:
    - uses: actions/checkout@v4

    - name: install libpfm
      run: |
        sudo apt update
        sudo apt -y install libpfm4-dev

    - name: create build environment
      run: cmake -E make_directory ${{ runner.workspace }}/_build

    - name: configure cmake
      shell: bash
      working-directory: ${{ runner.workspace }}/_build
      run: >
        cmake $GITHUB_WORKSPACE
        -DBENCHMARK_ENABLE_LIBPFM=1
        -DBENCHMARK_DOWNLOAD_DEPENDENCIES=ON
        -DCMAKE_BUILD_TYPE=${{ matrix.build_type }}

    - name: build
      shell: bash
      working-directory: ${{ runner.workspace }}/_build
      run: cmake --build . --config ${{ matrix.build_type }}

    # Skip testing, for now. It seems perf_event_open does not succeed on the
    # hosting machine, very likely a permissions issue.
    # TODO(mtrofin): Enable test.
    # - name: test
    #   shell: bash
    #   working-directory: ${{ runner.workspace }}/_build
    #   run: ctest -C ${{ matrix.build_type }} --rerun-failed --output-on-failure

