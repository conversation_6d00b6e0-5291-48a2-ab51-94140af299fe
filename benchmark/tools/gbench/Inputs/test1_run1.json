{"context": {"date": "2016-08-02 17:44:46", "num_cpus": 4, "mhz_per_cpu": 4228, "cpu_scaling_enabled": false, "library_build_type": "release"}, "benchmarks": [{"name": "BM_SameTimes", "iterations": 1000, "real_time": 10, "cpu_time": 10, "time_unit": "ns"}, {"name": "BM_2xFaster", "iterations": 1000, "real_time": 50, "cpu_time": 50, "time_unit": "ns"}, {"name": "BM_2xSlower", "iterations": 1000, "real_time": 50, "cpu_time": 50, "time_unit": "ns"}, {"name": "BM_1PercentFaster", "iterations": 1000, "real_time": 100, "cpu_time": 100, "time_unit": "ns"}, {"name": "BM_1PercentSlower", "iterations": 1000, "real_time": 100, "cpu_time": 100, "time_unit": "ns"}, {"name": "BM_10PercentFaster", "iterations": 1000, "real_time": 100, "cpu_time": 100, "time_unit": "ns"}, {"name": "BM_10PercentSlower", "iterations": 1000, "real_time": 100, "cpu_time": 100, "time_unit": "ns"}, {"name": "BM_100xSlower", "iterations": 1000, "real_time": 100, "cpu_time": 100, "time_unit": "ns"}, {"name": "BM_100xFaster", "iterations": 1000, "real_time": 10000, "cpu_time": 10000, "time_unit": "ns"}, {"name": "BM_10PercentCPUToTime", "iterations": 1000, "real_time": 100, "cpu_time": 100, "time_unit": "ns"}, {"name": "BM_ThirdFaster", "iterations": 1000, "real_time": 100, "cpu_time": 100, "time_unit": "ns"}, {"name": "MyComplexityTest_BigO", "run_name": "MyComplexityTest", "run_type": "aggregate", "aggregate_name": "BigO", "cpu_coefficient": 4.274985629459288, "real_coefficient": 6.478927528978978, "big_o": "N", "time_unit": "ns"}, {"name": "MyComplexityTest_RMS", "run_name": "MyComplexityTest", "run_type": "aggregate", "aggregate_name": "RMS", "rms": 0.004509780251247287}, {"name": "BM_NotBadTimeUnit", "iterations": 1000, "real_time": 0.4, "cpu_time": 0.5, "time_unit": "s"}, {"name": "BM_DifferentTimeUnit", "iterations": 1, "real_time": 1, "cpu_time": 1, "time_unit": "s"}, {"name": "BM_hasLabel", "label": "a label", "iterations": 1, "real_time": 1, "cpu_time": 1, "time_unit": "s"}]}